'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Award } from 'lucide-react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Project } from '@/types/project';

export default function WorkPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [categories, setCategories] = useState<string[]>(['All']);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      console.log('Fetching projects from Firebase...');
      
      // Try with orderBy first
      let projectsData: Project[] = [];
      try {
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active'),
          orderBy('order', 'asc')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
      } catch (indexError) {
        console.log('OrderBy failed, trying without orderBy');
        // Fallback: fetch without orderBy
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
        
        // Sort manually
        projectsData.sort((a, b) => (a.order || 0) - (b.order || 0));
      }

      console.log('Fetched projects:', projectsData.length);
      setProjects(projectsData);

      // Extract unique categories
      const uniqueCategories = ['All', ...new Set(projectsData.map(p => p.category))];
      setCategories(uniqueCategories);
    } catch (error) {
      console.error('Error fetching projects:', error);
      
      // Set empty arrays when error occurs
      setProjects([]);
      setCategories(['All']);
    } finally {
      setLoading(false);
    }
  };

  const filteredProjects = projects.filter(project => 
    selectedCategory === 'All' || project.category === selectedCategory
  );



  if (loading) {
    return (
      <div className="container mx-auto px-4 py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading our amazing work...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Hero Section */}
      <section className="py-16 sm:py-20 lg:py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            {/* <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
              <Award className="h-8 w-8 text-white" />
            </div> */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              A glimpse into our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">projects</span>
            </h1>
            <p className="text-xl sm:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              A look at some of the amazing webapps that we've built recently.
            </p>
          </div>



          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-3 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="pb-16 sm:pb-20 lg:pb-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-8 lg:gap-12">
            {filteredProjects.map((project, index) => (
              <div key={project.id} className="group">
                <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg">
                  {/* Mobile Layout: Image first, then content below */}
                  <div className="block lg:hidden">
                    {/* Mobile Image */}
                    <div className="aspect-video relative overflow-hidden">
                      <img
                        src={project.images[0] || 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop'}
                        alt={project.title}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Mobile Content */}
                    <div className="p-4 space-y-4">
                      <Badge variant="secondary" className="w-fit">
                        {project.category}
                      </Badge>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                          {project.title}
                        </h3>
                        <p className="text-gray-600 text-sm leading-relaxed line-clamp-3 mb-4">
                          {project.description}
                        </p>
                      </div>

                      {/* Technologies - Show only first 3 on mobile */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.technologies.slice(0, 3).map((tech, techIndex) => (
                          <Badge key={techIndex} variant="outline" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                        {project.technologies.length > 3 && (
                          <Badge variant="outline" className="text-xs text-gray-500">
                            +{project.technologies.length - 3} more
                          </Badge>
                        )}
                      </div>

                      {/* Mobile Action Button */}
                      {project.livePreviewUrl && (
                        <Button asChild className="w-full">
                          <a href={project.livePreviewUrl} target="_blank" rel="noopener noreferrer">
                            Live Preview
                            <ExternalLink className="ml-2 h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Desktop Layout: Side by side */}
                  <div className="hidden lg:flex">
                    {/* Desktop Image */}
                    <div className="w-3/5 relative">
                      <div className="aspect-video relative overflow-hidden">
                        <img
                          src={project.images[0] || 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop'}
                          alt={project.title}
                          className="w-full h-full object-cover transition duration-300 hover:scale-105"
                        />
                        {/* Overlay for better text readability */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                    </div>

                    {/* Desktop Content */}
                    <div className="w-2/5 p-8 lg:p-10 flex flex-col justify-center">
                      <div className="mb-6">
                        <Badge variant="secondary" className="mb-4">
                          {project.category}
                        </Badge>
                        <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                          {project.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed mb-6">
                          {project.description}
                        </p>
                        <div className="flex flex-wrap gap-2 mb-6">
                          {project.technologies.map((tech, techIndex) => (
                            <Badge key={techIndex} variant="outline" className="text-xs">
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      {project.livePreviewUrl && (
                        <Button asChild className="w-fit">
                          <a href={project.livePreviewUrl} target="_blank" rel="noopener noreferrer">
                            Live Preview
                            <ExternalLink className="ml-2 h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && !loading && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                {projects.length === 0 ? (
                  <svg className="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                ) : (
                  <Award className="h-16 w-16 mx-auto" />
                )}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {projects.length === 0 ? 'No projects available' : 'No projects found'}
              </h3>
              <p className="text-gray-600">
                {projects.length === 0
                  ? 'We are currently updating our project portfolio. Please check back soon.'
                  : selectedCategory === 'All'
                    ? 'No projects available at the moment.'
                    : `No projects found in "${selectedCategory}" category.`
                }
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
