'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { GraduationCap, Plus, Edit, Trash2, Calendar, Save, X } from 'lucide-react';
import { getInternships, createInternship, updateInternship, deleteInternship, Internship } from '@/lib/firebaseServices';
import { toast } from 'sonner';

interface InternshipsTabProps {
  onRefresh?: () => void;
}

export default function InternshipsTab({ onRefresh }: InternshipsTabProps) {
  const [internships, setInternships] = useState<Internship[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingInternship, setEditingInternship] = useState<Internship | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    department: '',
    duration: '',
    type: '',
    level: '',
    description: '',
    skills: '',
    projects: '',
    stipend: '',
    active: true
  });

  useEffect(() => {
    fetchInternships();
  }, []);

  const fetchInternships = async () => {
    try {
      setLoading(true);
      const fetchedInternships = await getInternships();
      setInternships(fetchedInternships);
    } catch (error) {
      console.error('Error fetching internships:', error);
      toast.error('Failed to load internships');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }
    if (!formData.department.trim()) {
      toast.error('Department is required');
      return;
    }
    if (!formData.description.trim()) {
      toast.error('Description is required');
      return;
    }

    try {
      const internshipData = {
        ...formData,
        skills: formData.skills ? formData.skills.split('\n').map(skill => skill.trim()).filter(Boolean) : [],
        projects: formData.projects ? formData.projects.split('\n').map(project => project.trim()).filter(Boolean) : []
      };

      if (editingInternship) {
        await updateInternship(editingInternship.id, internshipData);
        toast.success('Internship updated successfully!');
      } else {
        await createInternship(internshipData);
        toast.success('Internship created successfully!');
      }

      await fetchInternships();
      handleCloseDialog();
      onRefresh?.();
    } catch (error) {
      console.error('Error saving internship:', error);
      toast.error('Failed to save internship. Please try again.');
    }
  };

  const handleEdit = (internship: Internship) => {
    setEditingInternship(internship);
    setFormData({
      title: internship.title,
      department: internship.department,
      duration: internship.duration,
      type: internship.type,
      level: internship.level,
      description: internship.description,
      skills: internship.skills.join('\n'),
      projects: internship.projects.join('\n'),
      stipend: internship.stipend,
      active: internship.active
    });
    setDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this internship?')) return;

    try {
      await deleteInternship(id);
      toast.success('Internship deleted successfully!');
      await fetchInternships();
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting internship:', error);
      toast.error('Failed to delete internship. Please try again.');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingInternship(null);
    setFormData({
      title: '',
      department: '',
      duration: '',
      type: '',
      level: '',
      description: '',
      skills: '',
      projects: '',
      stipend: '',
      active: true
    });
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Internship Management</h2>
            <p className="text-muted-foreground">Loading internships...</p>
          </div>
        </div>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <GraduationCap className="h-6 w-6 text-green-600" />
            <h2 className="text-2xl font-bold">Internship Management</h2>
          </div>
          <p className="text-muted-foreground">
            Manage internship programs and opportunities
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Internship
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingInternship ? 'Edit Internship' : 'Add New Internship'}
              </DialogTitle>
              <DialogDescription>
                {editingInternship ? 'Update the internship details' : 'Create a new internship program'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Program Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Frontend Development Intern"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">Department *</Label>
                  <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Engineering">Engineering</SelectItem>
                      <SelectItem value="Design">Design</SelectItem>
                      <SelectItem value="Marketing">Marketing</SelectItem>
                      <SelectItem value="Sales">Sales</SelectItem>
                      <SelectItem value="Operations">Operations</SelectItem>
                      <SelectItem value="HR">Human Resources</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration</Label>
                  <Input
                    id="duration"
                    value={formData.duration}
                    onChange={(e) => handleInputChange('duration', e.target.value)}
                    placeholder="3-6 months"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Program Type</Label>
                  <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Remote">Remote</SelectItem>
                      <SelectItem value="Hybrid">Hybrid</SelectItem>
                      <SelectItem value="On-site">On-site</SelectItem>
                      <SelectItem value="Remote/Hybrid">Remote/Hybrid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="level">Experience Level</Label>
                  <Select value={formData.level} onValueChange={(value) => handleInputChange('level', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Beginner">Beginner</SelectItem>
                      <SelectItem value="Intermediate">Intermediate</SelectItem>
                      <SelectItem value="Advanced">Advanced</SelectItem>
                      <SelectItem value="Beginner to Intermediate">Beginner to Intermediate</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stipend">Stipend (INR)</Label>
                <Input
                  id="stipend"
                  value={formData.stipend}
                  onChange={(e) => handleInputChange('stipend', e.target.value)}
                  placeholder="₹15,000 - ₹25,000/month"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Program Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe the internship program, what interns will learn and work on..."
                  rows={4}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="skills">Skills to Learn (one per line)</Label>
                <Textarea
                  id="skills"
                  value={formData.skills}
                  onChange={(e) => handleInputChange('skills', e.target.value)}
                  placeholder="React/Next.js&#10;JavaScript/TypeScript&#10;HTML/CSS&#10;Git/GitHub"
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="projects">Projects to Work On (one per line)</Label>
                <Textarea
                  id="projects"
                  value={formData.projects}
                  onChange={(e) => handleInputChange('projects', e.target.value)}
                  placeholder="Template marketplace features&#10;User dashboard improvements&#10;Mobile responsiveness"
                  rows={4}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked) => handleInputChange('active', checked)}
                />
                <Label htmlFor="active">Active (visible to applicants)</Label>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  {editingInternship ? 'Update' : 'Create'} Internship
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{internships.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Programs</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{internships.filter(internship => internship.active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Badge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{new Set(internships.map(internship => internship.department)).size}</div>
          </CardContent>
        </Card>
      </div>

      {/* Internships Table */}
      <Card>
        <CardHeader>
          <CardTitle>Internship Programs</CardTitle>
          <CardDescription>
            Manage internship programs and opportunities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Program</TableHead>
                <TableHead>Department</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {internships.map((internship) => (
                <TableRow key={internship.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{internship.title}</div>
                      <div className="text-xs text-muted-foreground">{internship.type} • {internship.level}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{internship.department}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3 mr-1" />
                      {internship.duration}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={internship.active ? "default" : "secondary"}>
                      {internship.active ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(internship)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(internship.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {internships.length === 0 && (
            <div className="text-center py-8">
              <GraduationCap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No internships found</h3>
              <p className="text-muted-foreground mb-4">
                Create your first internship program to get started
              </p>
              <Button onClick={() => setDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Internship
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
