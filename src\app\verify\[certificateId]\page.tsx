import CertificateVerification from '../../certificate/certificate';

// Generate static params for static export
export async function generateStaticParams() {
  // Return a single static param to make this work with static export
  // The actual certificate ID will be handled client-side
  return [{ certificateId: 'verify' }];
}

export default function VerifyCertificatePage() {
  return <CertificateVerification />;
}
