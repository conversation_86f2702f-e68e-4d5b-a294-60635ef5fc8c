'use client';

import React, { useState } from 'react';
import { Di<PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, User, Briefcase, GraduationCap, FileText, Calendar, Users } from 'lucide-react';
import { createApplication } from '@/lib/firebaseServices';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface ApplicationFormProps {
  isOpen: boolean;
  onClose: () => void;
  position: {
    id: string;
    title: string;
    type: 'career' | 'internship';
  } | null;
}

interface ApplicationData {
  // Contact Information (for non-logged users)
  email: string;
  phoneNumber: string;

  // Personal Information
  fullName: string;
  dateOfBirth: string;
  gender: string;
  nationality: string;
  currentAddress: string;
  permanentAddress: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelation: string;
  
  // Professional Information
  currentPosition: string;
  currentCompany: string;
  totalExperience: string;
  relevantExperience: string;
  currentSalary: string;
  expectedSalary: string;
  noticePeriod: string;
  
  // Education
  highestQualification: string;
  university: string;
  graduationYear: string;
  cgpa: string;
  additionalCertifications: string;
  
  // Skills & Experience
  technicalSkills: string;
  softSkills: string;
  languages: string;
  previousProjects: string;
  achievements: string;
  
  // Application Materials
  resume: string;
  coverLetter: string;
  portfolio: string;
  
  // Availability & Preferences
  availableStartDate: string;
  workPreference: 'remote' | 'hybrid' | 'onsite' | 'flexible';
  willingToRelocate: boolean;
  
  // Additional Information
  whyInterested: string;
  longTermGoals: string;
  additionalInfo: string;
  
  // References
  reference1Name: string;
  reference1Position: string;
  reference1Company: string;
  reference1Email: string;
  reference1Phone: string;
  reference2Name: string;
  reference2Position: string;
  reference2Company: string;
  reference2Email: string;
  reference2Phone: string;
}

export default function ApplicationForm({ isOpen, onClose, position }: ApplicationFormProps) {
  const { user, userData } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [currentTab, setCurrentTab] = useState('personal');

  // Always initialize all state hooks first
  const [showProfileError, setShowProfileError] = useState(false);

  // Initialize formData state hook here to avoid hooks order issues
  const [formData, setFormData] = useState<ApplicationData>({
    email: user?.email || '',
    phoneNumber: userData?.phoneNumber || '',
    fullName: userData?.fullName || '',
    dateOfBirth: '',
    gender: '',
    nationality: '',
    currentAddress: '',
    permanentAddress: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelation: '',
    currentPosition: '',
    currentCompany: '',
    totalExperience: '',
    relevantExperience: '',
    currentSalary: '',
    expectedSalary: '',
    noticePeriod: '',
    highestQualification: '',
    university: '',
    graduationYear: '',
    cgpa: '',
    additionalCertifications: '',
    technicalSkills: '',
    softSkills: '',
    languages: '',
    previousProjects: '',
    achievements: '',
    resume: '',
    coverLetter: '',
    portfolio: '',
    availableStartDate: '',
    workPreference: 'flexible',
    willingToRelocate: false,
    whyInterested: '',
    longTermGoals: '',
    additionalInfo: '',
    reference1Name: '',
    reference1Position: '',
    reference1Company: '',
    reference1Email: '',
    reference1Phone: '',
    reference2Name: '',
    reference2Position: '',
    reference2Company: '',
    reference2Email: '',
    reference2Phone: ''
  });

  // Check profile completion for logged users only
  React.useEffect(() => {
    if (isOpen) {
      if (user && !userData?.phoneNumber) {
        setShowProfileError(true);
      } else {
        setShowProfileError(false);
      }
    }
  }, [isOpen, user, userData]);

  // Don't render anything if no position is provided
  if (!position) {
    return null;
  }

  // Remove authentication error dialog - allow non-logged users to apply

  // Show profile completion error dialog
  if (showProfileError) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Complete Your Profile</DialogTitle>
            <DialogDescription>
              Please complete your profile with phone number before applying for positions.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-center pt-4">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const updateFormData = (field: keyof ApplicationData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateRequiredFields = () => {
    const requiredFields = [
      'fullName', 'currentAddress', 'highestQualification', 
      'coverLetter', 'whyInterested'
    ];
    
    for (const field of requiredFields) {
      if (!formData[field as keyof ApplicationData]) {
        toast.error(`Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field`);
        return false;
      }
    }
    return true;
  };

  const submitApplication = async () => {
    if (!position) return;

    if (!validateRequiredFields()) return;

    try {
      setSubmitting(true);
      await createApplication({
        type: position.type,
        positionId: position.id,
        positionTitle: position.title,
        userId: user?.uid,
        userEmail: user?.email || formData.email || '',
        userName: user ? (userData?.fullName || user.email!.split('@')[0]) : formData.fullName,
        userPhone: user ? `${userData?.countryCode || '+91'} ${userData?.phoneNumber}` : formData.phoneNumber,
        ...formData,
        status: 'pending'
      });

      toast.success('Application submitted successfully! We will contact you soon.');
      onClose();
      // Reset form
      setFormData({
        email: user?.email || '',
        phoneNumber: userData?.phoneNumber || '',
        fullName: userData?.fullName || '',
        dateOfBirth: '',
        gender: '',
        nationality: '',
        currentAddress: '',
        permanentAddress: '',
        emergencyContactName: '',
        emergencyContactPhone: '',
        emergencyContactRelation: '',
        currentPosition: '',
        currentCompany: '',
        totalExperience: '',
        relevantExperience: '',
        currentSalary: '',
        expectedSalary: '',
        noticePeriod: '',
        highestQualification: '',
        university: '',
        graduationYear: '',
        cgpa: '',
        additionalCertifications: '',
        technicalSkills: '',
        softSkills: '',
        languages: '',
        previousProjects: '',
        achievements: '',
        resume: '',
        coverLetter: '',
        portfolio: '',
        availableStartDate: '',
        workPreference: 'flexible',
        willingToRelocate: false,
        whyInterested: '',
        longTermGoals: '',
        additionalInfo: '',
        reference1Name: '',
        reference1Position: '',
        reference1Company: '',
        reference1Email: '',
        reference1Phone: '',
        reference2Name: '',
        reference2Position: '',
        reference2Company: '',
        reference2Email: '',
        reference2Phone: ''
      });
    } catch (error) {
      console.error('Error submitting application:', error);
      toast.error('Failed to submit application. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (!position) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden p-0">
        <div className="flex flex-col h-full">
          <DialogHeader className="px-6 py-4 border-b flex-shrink-0">
            <DialogTitle className="text-xl">Apply for {position.title}</DialogTitle>
            <DialogDescription>
              Please fill out this comprehensive application form. All fields marked with * are required.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs value={currentTab} onValueChange={setCurrentTab} className="h-full flex flex-col">
              <div className="px-6 py-4 border-b bg-gray-50 flex-shrink-0">
                <TabsList className="grid grid-cols-3 lg:grid-cols-6 gap-2 max-w-full mx-auto h-auto p-0 bg-transparent overflow-x-auto">
                  <TabsTrigger
                    value="personal"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all whitespace-nowrap"
                  >
                    <User className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Personal</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="professional"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all whitespace-nowrap"
                  >
                    <Briefcase className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Professional</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="education"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all whitespace-nowrap"
                  >
                    <GraduationCap className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Education</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="skills"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all whitespace-nowrap"
                  >
                    <FileText className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Skills</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="preferences"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all whitespace-nowrap"
                  >
                    <Calendar className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Preferences</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="references"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all whitespace-nowrap"
                  >
                    <Users className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">References</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <div className="flex-1 overflow-hidden">
                <style jsx global>{`
                  .custom-scrollbar {
                    scrollbar-width: none; /* Firefox */
                    -ms-overflow-style: none; /* Internet Explorer 10+ */
                  }
                  .custom-scrollbar::-webkit-scrollbar {
                    display: none; /* WebKit */
                  }
                `}</style>
                {/* Personal Information Tab */}
                <TabsContent value="personal" className="h-full overflow-y-auto px-6 py-4 custom-scrollbar">
                  <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
                <CardDescription>Basic personal details and contact information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {!user && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 p-4 bg-blue-50 rounded-lg">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData('email', e.target.value)}
                        placeholder="Enter your email address"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phoneNumber">Phone Number *</Label>
                      <Input
                        id="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={(e) => updateFormData('phoneNumber', e.target.value)}
                        placeholder="Enter your phone number"
                        required
                      />
                    </div>
                  </div>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name *</Label>
                    <Input
                      id="fullName"
                      value={formData.fullName}
                      onChange={(e) => updateFormData('fullName', e.target.value)}
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="gender">Gender</Label>
                    <Select value={formData.gender} onValueChange={(value) => updateFormData('gender', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                        <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationality">Nationality</Label>
                    <Input
                      id="nationality"
                      value={formData.nationality}
                      onChange={(e) => updateFormData('nationality', e.target.value)}
                      placeholder="e.g., Indian, American"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currentAddress">Current Address *</Label>
                  <Textarea
                    id="currentAddress"
                    value={formData.currentAddress}
                    onChange={(e) => updateFormData('currentAddress', e.target.value)}
                    placeholder="Enter your current address"
                    rows={3}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="permanentAddress">Permanent Address (if different)</Label>
                  <Textarea
                    id="permanentAddress"
                    value={formData.permanentAddress}
                    onChange={(e) => updateFormData('permanentAddress', e.target.value)}
                    placeholder="Enter your permanent address"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="emergencyContactName">Emergency Contact Name</Label>
                    <Input
                      id="emergencyContactName"
                      value={formData.emergencyContactName}
                      onChange={(e) => updateFormData('emergencyContactName', e.target.value)}
                      placeholder="Contact person name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="emergencyContactPhone">Emergency Contact Phone</Label>
                    <Input
                      id="emergencyContactPhone"
                      value={formData.emergencyContactPhone}
                      onChange={(e) => updateFormData('emergencyContactPhone', e.target.value)}
                      placeholder="Contact phone number"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="emergencyContactRelation">Relationship</Label>
                    <Input
                      id="emergencyContactRelation"
                      value={formData.emergencyContactRelation}
                      onChange={(e) => updateFormData('emergencyContactRelation', e.target.value)}
                      placeholder="e.g., Father, Mother, Spouse"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
                  </div>
                </TabsContent>

                {/* Professional Information Tab */}
                <TabsContent value="professional" className="h-full overflow-y-auto px-6 py-4 custom-scrollbar">
                  <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Professional Information</CardTitle>
                <CardDescription>Current employment and work experience details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPosition">Current Position</Label>
                    <Input
                      id="currentPosition"
                      value={formData.currentPosition}
                      onChange={(e) => updateFormData('currentPosition', e.target.value)}
                      placeholder="e.g., Software Developer, Student"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currentCompany">Current Company/Institution</Label>
                    <Input
                      id="currentCompany"
                      value={formData.currentCompany}
                      onChange={(e) => updateFormData('currentCompany', e.target.value)}
                      placeholder="Company or university name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="totalExperience">Total Experience</Label>
                    <Select value={formData.totalExperience} onValueChange={(value) => updateFormData('totalExperience', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select experience" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fresher">Fresher (0 years)</SelectItem>
                        <SelectItem value="0-1">0-1 years</SelectItem>
                        <SelectItem value="1-2">1-2 years</SelectItem>
                        <SelectItem value="2-3">2-3 years</SelectItem>
                        <SelectItem value="3-5">3-5 years</SelectItem>
                        <SelectItem value="5-7">5-7 years</SelectItem>
                        <SelectItem value="7-10">7-10 years</SelectItem>
                        <SelectItem value="10+">10+ years</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="relevantExperience">Relevant Experience</Label>
                    <Input
                      id="relevantExperience"
                      value={formData.relevantExperience}
                      onChange={(e) => updateFormData('relevantExperience', e.target.value)}
                      placeholder="Years of relevant experience"
                    />
                  </div>
                </div>

                {position.type === 'career' && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="currentSalary">Current Salary (₹)</Label>
                      <Input
                        id="currentSalary"
                        value={formData.currentSalary}
                        onChange={(e) => updateFormData('currentSalary', e.target.value)}
                        placeholder="Current salary per annum"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="expectedSalary">Expected Salary (₹)</Label>
                      <Input
                        id="expectedSalary"
                        value={formData.expectedSalary}
                        onChange={(e) => updateFormData('expectedSalary', e.target.value)}
                        placeholder="Expected salary per annum"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="noticePeriod">Notice Period</Label>
                      <Select value={formData.noticePeriod} onValueChange={(value) => updateFormData('noticePeriod', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select notice period" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="immediate">Immediate</SelectItem>
                          <SelectItem value="15-days">15 days</SelectItem>
                          <SelectItem value="1-month">1 month</SelectItem>
                          <SelectItem value="2-months">2 months</SelectItem>
                          <SelectItem value="3-months">3 months</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
                  </div>
                </TabsContent>

                {/* Education Tab */}
                <TabsContent value="education" className="h-full overflow-y-auto px-6 py-4 custom-scrollbar">
                  <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Education & Qualifications</CardTitle>
                <CardDescription>Academic background and certifications</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="highestQualification">Highest Qualification *</Label>
                    <Select value={formData.highestQualification} onValueChange={(value) => updateFormData('highestQualification', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select qualification" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high-school">High School (12th)</SelectItem>
                        <SelectItem value="diploma">Diploma</SelectItem>
                        <SelectItem value="bachelors">Bachelor's Degree</SelectItem>
                        <SelectItem value="masters">Master's Degree</SelectItem>
                        <SelectItem value="phd">PhD</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="university">University/Institution</Label>
                    <Input
                      id="university"
                      value={formData.university}
                      onChange={(e) => updateFormData('university', e.target.value)}
                      placeholder="Name of university or institution"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="graduationYear">Graduation Year</Label>
                    <Input
                      id="graduationYear"
                      value={formData.graduationYear}
                      onChange={(e) => updateFormData('graduationYear', e.target.value)}
                      placeholder="e.g., 2023"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cgpa">CGPA/Percentage</Label>
                    <Input
                      id="cgpa"
                      value={formData.cgpa}
                      onChange={(e) => updateFormData('cgpa', e.target.value)}
                      placeholder="e.g., 8.5 CGPA or 85%"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="additionalCertifications">Additional Certifications</Label>
                  <Textarea
                    id="additionalCertifications"
                    value={formData.additionalCertifications}
                    onChange={(e) => updateFormData('additionalCertifications', e.target.value)}
                    placeholder="List any relevant certifications, courses, or training programs"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
                  </div>
                </TabsContent>

                {/* Skills Tab */}
                <TabsContent value="skills" className="h-full overflow-y-auto px-6 py-4 custom-scrollbar">
                  <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Skills & Experience</CardTitle>
                <CardDescription>Technical skills, projects, and achievements</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="technicalSkills">Technical Skills</Label>
                  <Textarea
                    id="technicalSkills"
                    value={formData.technicalSkills}
                    onChange={(e) => updateFormData('technicalSkills', e.target.value)}
                    placeholder="List your technical skills (programming languages, frameworks, tools, etc.)"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="softSkills">Soft Skills</Label>
                  <Textarea
                    id="softSkills"
                    value={formData.softSkills}
                    onChange={(e) => updateFormData('softSkills', e.target.value)}
                    placeholder="Communication, leadership, teamwork, problem-solving, etc."
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="languages">Languages Known</Label>
                  <Input
                    id="languages"
                    value={formData.languages}
                    onChange={(e) => updateFormData('languages', e.target.value)}
                    placeholder="e.g., English (Fluent), Hindi (Native), Spanish (Basic)"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="previousProjects">Previous Projects</Label>
                  <Textarea
                    id="previousProjects"
                    value={formData.previousProjects}
                    onChange={(e) => updateFormData('previousProjects', e.target.value)}
                    placeholder="Describe your key projects, including technologies used and your role"
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="achievements">Achievements & Awards</Label>
                  <Textarea
                    id="achievements"
                    value={formData.achievements}
                    onChange={(e) => updateFormData('achievements', e.target.value)}
                    placeholder="Academic achievements, awards, recognitions, publications, etc."
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="resume">Resume/CV Link</Label>
                    <Input
                      id="resume"
                      value={formData.resume}
                      onChange={(e) => updateFormData('resume', e.target.value)}
                      placeholder="Google Drive, Dropbox, etc."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="portfolio">Portfolio Link</Label>
                    <Input
                      id="portfolio"
                      value={formData.portfolio}
                      onChange={(e) => updateFormData('portfolio', e.target.value)}
                      placeholder="GitHub, personal website, etc."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="coverLetter">Cover Letter *</Label>
                    <Textarea
                      id="coverLetter"
                      value={formData.coverLetter}
                      onChange={(e) => updateFormData('coverLetter', e.target.value)}
                      placeholder="Why are you interested in this position?"
                      rows={4}
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
                  </div>
                </TabsContent>

                {/* Preferences Tab */}
                <TabsContent value="preferences" className="h-full overflow-y-auto px-6 py-4 custom-scrollbar">
                  <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Availability & Preferences</CardTitle>
                <CardDescription>Work preferences and availability details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="availableStartDate">Available Start Date</Label>
                    <Input
                      id="availableStartDate"
                      type="date"
                      value={formData.availableStartDate}
                      onChange={(e) => updateFormData('availableStartDate', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="workPreference">Work Preference</Label>
                    <Select value={formData.workPreference} onValueChange={(value) => updateFormData('workPreference', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select work preference" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="remote">Remote</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                        <SelectItem value="onsite">On-site</SelectItem>
                        <SelectItem value="flexible">Flexible</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="willingToRelocate"
                    checked={formData.willingToRelocate}
                    onCheckedChange={(checked) => updateFormData('willingToRelocate', checked as boolean)}
                  />
                  <Label htmlFor="willingToRelocate">Willing to relocate if required</Label>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="whyInterested">Why are you interested in this position? *</Label>
                  <Textarea
                    id="whyInterested"
                    value={formData.whyInterested}
                    onChange={(e) => updateFormData('whyInterested', e.target.value)}
                    placeholder="Explain your interest in this role and our company"
                    rows={4}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="longTermGoals">Long-term Career Goals</Label>
                  <Textarea
                    id="longTermGoals"
                    value={formData.longTermGoals}
                    onChange={(e) => updateFormData('longTermGoals', e.target.value)}
                    placeholder="Where do you see yourself in 3-5 years?"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="additionalInfo">Additional Information</Label>
                  <Textarea
                    id="additionalInfo"
                    value={formData.additionalInfo}
                    onChange={(e) => updateFormData('additionalInfo', e.target.value)}
                    placeholder="Any additional information you'd like to share"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
                  </div>
                </TabsContent>

                {/* References Tab */}
                <TabsContent value="references" className="h-full overflow-y-auto px-6 py-4 custom-scrollbar">
                  <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Professional References</CardTitle>
                <CardDescription>Provide at least one professional reference</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-3">Reference 1</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="reference1Name">Name</Label>
                      <Input
                        id="reference1Name"
                        value={formData.reference1Name}
                        onChange={(e) => updateFormData('reference1Name', e.target.value)}
                        placeholder="Reference name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference1Position">Position</Label>
                      <Input
                        id="reference1Position"
                        value={formData.reference1Position}
                        onChange={(e) => updateFormData('reference1Position', e.target.value)}
                        placeholder="Job title"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference1Company">Company</Label>
                      <Input
                        id="reference1Company"
                        value={formData.reference1Company}
                        onChange={(e) => updateFormData('reference1Company', e.target.value)}
                        placeholder="Company name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference1Email">Email</Label>
                      <Input
                        id="reference1Email"
                        type="email"
                        value={formData.reference1Email}
                        onChange={(e) => updateFormData('reference1Email', e.target.value)}
                        placeholder="Email address"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference1Phone">Phone</Label>
                      <Input
                        id="reference1Phone"
                        value={formData.reference1Phone}
                        onChange={(e) => updateFormData('reference1Phone', e.target.value)}
                        placeholder="Phone number"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Reference 2 (Optional)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="reference2Name">Name</Label>
                      <Input
                        id="reference2Name"
                        value={formData.reference2Name}
                        onChange={(e) => updateFormData('reference2Name', e.target.value)}
                        placeholder="Reference name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference2Position">Position</Label>
                      <Input
                        id="reference2Position"
                        value={formData.reference2Position}
                        onChange={(e) => updateFormData('reference2Position', e.target.value)}
                        placeholder="Job title"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference2Company">Company</Label>
                      <Input
                        id="reference2Company"
                        value={formData.reference2Company}
                        onChange={(e) => updateFormData('reference2Company', e.target.value)}
                        placeholder="Company name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference2Email">Email</Label>
                      <Input
                        id="reference2Email"
                        type="email"
                        value={formData.reference2Email}
                        onChange={(e) => updateFormData('reference2Email', e.target.value)}
                        placeholder="Email address"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="reference2Phone">Phone</Label>
                      <Input
                        id="reference2Phone"
                        value={formData.reference2Phone}
                        onChange={(e) => updateFormData('reference2Phone', e.target.value)}
                        placeholder="Phone number"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>

          {/* Form Actions */}
          <div className="px-6 py-4 border-t bg-gray-50 flex-shrink-0">
            <div className="flex flex-col sm:flex-row justify-between gap-3">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={submitting}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                onClick={submitApplication}
                disabled={submitting || !formData.fullName || !formData.currentAddress || !formData.highestQualification || !formData.coverLetter || !formData.whyInterested}
                className="w-full sm:w-auto"
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting Application...
                  </>
                ) : (
                  'Submit Application'
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
