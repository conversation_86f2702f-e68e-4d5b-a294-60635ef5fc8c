'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Award, Plus, Edit, Trash2, QrCode, Save, X, Calendar, User, GraduationCap } from 'lucide-react';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { toast } from 'sonner';
import { QRCodeCanvas } from 'qrcode.react';

interface Certificate {
  id: string;
  certificateCode?: string;
  name: string;
  course: string;
  startDate: string;
  endDate: string;
  grade: string;
  lor: string;
  institution: string;
  createdAt: Date;
}

interface CertificatesTabProps {
  onRefresh?: () => void;
}

export const CertificatesTab: React.FC<CertificatesTabProps> = ({ onRefresh }) => {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [qrDialogOpen, setQrDialogOpen] = useState(false);
  const [selectedCertificate, setSelectedCertificate] = useState<Certificate | null>(null);
  const [editingCertificate, setEditingCertificate] = useState<Certificate | null>(null);
  const [formData, setFormData] = useState({
    certificateCode: '',
    name: '',
    course: '',
    startDate: '',
    endDate: '',
    grade: '',
    lor: '',
    institution: 'KaleidoNex Technologies'
  });

  useEffect(() => {
    fetchCertificates();
  }, []);

  const generateCertificateCode = () => {
    const prefix = 'KN'; // KaleidoNex prefix
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const random = Math.random().toString(36).substring(2, 6).toUpperCase(); // 4 random chars
    return `${prefix}${timestamp}${random}`;
  };

  const fetchCertificates = async () => {
    try {
      setLoading(true);
      // Add timeout for slow networks
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 15000)
      );

      const fetchPromise = getDocs(query(collection(db, 'certificate'), orderBy('createdAt', 'desc')));

      const querySnapshot = await Promise.race([fetchPromise, timeoutPromise]) as any;

      const certificatesData = querySnapshot.docs.map((doc: any) => ({
        id: doc.id,
        certificateCode: doc.data().certificateCode || doc.id, // Use certificateCode or fallback to doc ID
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as Certificate[];

      setCertificates(certificatesData);
    } catch (error: any) {
      console.error('Error fetching certificates:', error);
      if (error.message === 'Request timeout') {
        toast.error('Request timed out. Please check your internet connection and try again.');
      } else {
        toast.error('Failed to fetch certificates. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.course || !formData.startDate || !formData.endDate || !formData.grade) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      if (editingCertificate) {
        await updateDoc(doc(db, 'certificate', editingCertificate.id), {
          ...formData,
          updatedAt: new Date()
        });
        toast.success('Certificate updated successfully');
      } else {
        // Generate certificate code if not provided
        const certificateCode = formData.certificateCode || generateCertificateCode();

        await addDoc(collection(db, 'certificate'), {
          ...formData,
          certificateCode,
          createdAt: new Date()
        });
        toast.success(`Certificate created successfully with code: ${certificateCode}`);
      }

      setDialogOpen(false);
      resetForm();
      fetchCertificates();
      onRefresh?.();
    } catch (error) {
      console.error('Error saving certificate:', error);
      toast.error('Failed to save certificate');
    }
  };

  const handleEdit = (certificate: Certificate) => {
    setEditingCertificate(certificate);
    setFormData({
      certificateCode: certificate.certificateCode || '',
      name: certificate.name,
      course: certificate.course,
      startDate: certificate.startDate,
      endDate: certificate.endDate,
      grade: certificate.grade,
      lor: certificate.lor,
      institution: certificate.institution
    });
    setDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this certificate?')) return;
    
    try {
      await deleteDoc(doc(db, 'certificate', id));
      toast.success('Certificate deleted successfully');
      fetchCertificates();
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting certificate:', error);
      toast.error('Failed to delete certificate');
    }
  };

  const resetForm = () => {
    setFormData({
      certificateCode: '',
      name: '',
      course: '',
      startDate: '',
      endDate: '',
      grade: '',
      lor: '',
      institution: 'KaleidoNex Technologies'
    });
    setEditingCertificate(null);
  };

  const showQRCode = (certificate: Certificate) => {
    setSelectedCertificate(certificate);
    setQrDialogOpen(true);
  };

  const getVerificationUrl = (certificateId: string) => {
    return `${window.location.origin}/verify/${certificateId}`;
  };

  const downloadQR = (certificate: Certificate) => {
    const canvas = document.querySelector(`#qr-${certificate.id} canvas`) as HTMLCanvasElement;
    if (!canvas) return;
    
    const image = canvas.toDataURL('image/png');
    const link = document.createElement('a');
    link.href = image;
    link.download = `certificate-${certificate.id}-qr.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Award className="h-6 w-6 text-yellow-600" />
            <h2 className="text-2xl font-bold">Certificate Management</h2>
          </div>
          <p className="text-muted-foreground">
            Create and manage certificates for students and participants
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Certificate
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingCertificate ? 'Edit Certificate' : 'Create New Certificate'}
              </DialogTitle>
              <DialogDescription>
                Fill in the details to {editingCertificate ? 'update' : 'create'} a certificate
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="certificateCode">Certificate Code (Optional)</Label>
                <div className="flex gap-2">
                  <Input
                    id="certificateCode"
                    value={formData.certificateCode}
                    onChange={(e) => setFormData({ ...formData, certificateCode: e.target.value })}
                    placeholder="Leave empty to auto-generate"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setFormData({ ...formData, certificateCode: generateCertificateCode() })}
                  >
                    Generate
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  If left empty, a unique code will be generated automatically (e.g., KN123456ABCD)
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Student Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Enter student name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="course">Department *</Label>
                  <Input
                    id="course"
                    value={formData.course}
                    onChange={(e) => setFormData({ ...formData, course: e.target.value })}
                    placeholder="e.g., Computer Science, Information Technology"
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date *</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="grade">Project Name *</Label>
                  <Input
                    id="grade"
                    value={formData.grade}
                    onChange={(e) => setFormData({ ...formData, grade: e.target.value })}
                    placeholder="e.g., E-commerce Website, Mobile App Development"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="institution">Institution</Label>
                  <Input
                    id="institution"
                    value={formData.institution}
                    onChange={(e) => setFormData({ ...formData, institution: e.target.value })}
                    placeholder="Institution name"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="lor">Mentor Name</Label>
                <Input
                  id="lor"
                  value={formData.lor}
                  onChange={(e) => setFormData({ ...formData, lor: e.target.value })}
                  placeholder="Enter mentor or supervisor name"
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setDialogOpen(false);
                    resetForm();
                  }}
                >
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  {editingCertificate ? 'Update' : 'Create'} Certificate
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Certificates Table */}
      <Card>
        <CardHeader>
          <CardTitle>Certificates ({certificates.length})</CardTitle>
          <CardDescription>
            Manage all issued certificates
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading certificates...</h3>
              <p className="text-gray-600 text-sm">
                This might take a moment on slower connections
              </p>
              <div className="mt-4">
                <Button
                  variant="outline"
                  onClick={fetchCertificates}
                  className="text-sm"
                >
                  Retry if taking too long
                </Button>
              </div>
            </div>
          ) : certificates.length === 0 ? (
            <div className="text-center py-8">
              <Award className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No certificates found</h3>
              <p className="text-gray-600 mb-4">Create your first certificate to get started.</p>
              <Button onClick={() => setDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Certificate
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Certificate Code</TableHead>
                    <TableHead>Student</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {certificates.map((certificate) => (
                    <TableRow key={certificate.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Award className="h-4 w-4 text-yellow-500" />
                          <span className="font-mono text-sm font-medium">
                            {certificate.certificateCode || certificate.id}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">{certificate.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <GraduationCap className="h-4 w-4 text-blue-500" />
                          <span>{certificate.course}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-green-500" />
                          <span className="text-sm">
                            {certificate.startDate} to {certificate.endDate}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{certificate.grade}</Badge>
                      </TableCell>
                      <TableCell className="text-sm text-gray-500">
                        {certificate.createdAt.toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => showQRCode(certificate)}
                          >
                            <QrCode className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(certificate)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(certificate.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* QR Code Dialog */}
      <Dialog open={qrDialogOpen} onOpenChange={setQrDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Certificate QR Code</DialogTitle>
            <DialogDescription>
              Scan this QR code to verify the certificate
            </DialogDescription>
          </DialogHeader>
          {selectedCertificate && (
            <div className="text-center space-y-4">
              <div className="bg-white p-4 rounded-lg inline-block">
                <div id={`qr-${selectedCertificate.id}`}>
                  <QRCodeCanvas
                    value={getVerificationUrl(selectedCertificate.certificateCode || selectedCertificate.id)}
                    size={200}
                    level="H"
                    marginSize={4}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  <strong>{selectedCertificate.name}</strong>
                </p>
                <p className="text-xs text-gray-500">
                  Certificate ID: {selectedCertificate.id}
                </p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => downloadQR(selectedCertificate)}
                  className="flex-1"
                >
                  Download QR
                </Button>
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(getVerificationUrl(selectedCertificate.id));
                    toast.success('Verification URL copied to clipboard');
                  }}
                  className="flex-1"
                >
                  Copy URL
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
