# KaleidoneX - Premium Template Marketplace

A modern, professional template marketplace built with Next.js, shadcn/ui, Firebase, and Tailwind CSS. Features role-based authentication, admin dashboard, template management, and a complete purchase system.

## 🚀 Features

### User Features
- **Browse Templates**: Explore premium templates with search and filtering
- **Categories**: Organized template categories for easy discovery
- **User Dashboard**: Manage purchases, downloads, and favorites
- **Custom Requests**: Request custom designs for specific needs
- **Purchase System**: Secure template purchasing with order management
- **Responsive Design**: Mobile-first, professional UI/UX

### Admin Features
- **Admin Dashboard**: Comprehensive analytics and management
- **Template Management**: Add, edit, and manage templates
- **Order Management**: Confirm/decline orders and track sales
- **User Management**: Monitor user activity and accounts
- **Custom Request Handling**: Manage custom design requests
- **Export Functionality**: Export data and reports

### Technical Features
- **Role-based Authentication**: Admin and user roles with Firebase Auth
- **Real-time Database**: Firestore for data management
- **Modern UI**: shadcn/ui components with Tailwind CSS
- **TypeScript**: Full type safety throughout the application
- **Responsive Design**: Mobile-first approach with professional aesthetics

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **UI Library**: shadcn/ui + Tailwind CSS
- **Authentication**: Firebase Auth
- **Database**: Firestore
- **Language**: TypeScript
- **Icons**: Lucide React
- **Forms**: React Hook Form + Zod validation

## 📦 Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up Firebase**
   - Create a new Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Authentication (Email/Password)
   - Create a Firestore database
   - Get your Firebase configuration

3. **Environment Variables**
   ```bash
   cp .env.example .env.local
   ```

   Fill in your Firebase configuration in `.env.local`:
   ```env
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎨 Pages Structure

```
/                    # Home page with hero, featured templates, categories
/auth               # Authentication (sign in/sign up)
/favorites          # User favorites (replaces dashboard)
/admin              # Admin dashboard (admin only)
/templates          # Browse all templates
/categories         # Browse categories
/custom-request     # Request custom design
/orders             # User orders & custom requests
/contact            # Contact page
```

## 🔐 User Roles

- **User**: Can browse, purchase, and download templates
- **Admin**: Full access to manage templates, orders, and users

Built with ❤️ using Next.js, shadcn/ui, and Firebase
