'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Bot, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  MessageCircle,
  Zap,
  Eye,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';

interface AITool {
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  icon: string;
  features: string[];
  buttonText: string;
  active: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

interface AIToolkitTabProps {
  onRefresh?: () => void;
}

export default function AIToolkitTab({ onRefresh }: AIToolkitTabProps) {
  const [tools, setTools] = useState<AITool[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingTool, setEditingTool] = useState<AITool | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    videoUrl: '',
    icon: '',
    features: '',
    buttonText: '',
    active: true,
    order: 0
  });

  // Mock data for now - in real implementation, this would come from Firebase
  const mockTools: AITool[] = [
    {
      id: 'whatsapp',
      title: 'WhatsApp Business Automation',
      description: 'Automate customer conversations, responses, and notifications on WhatsApp Business.',
      videoUrl: 'https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20231743.mp4?updatedAt=1756484517549',
      icon: 'MessageCircle',
      features: ['Auto Replies', 'Customer Support', 'Bulk Messaging'],
      buttonText: 'Automate WhatsApp',
      active: true,
      order: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'instagram',
      title: 'Instagram Business Automation',
      description: 'Automate Instagram DMs, comments, and engagement with AI-powered responses.',
      videoUrl: 'https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20232056.mp4?updatedAt=1756484517549',
      icon: 'Instagram',
      features: ['Auto DM Replies', 'Comment Management', 'Engagement Boost'],
      buttonText: 'Automate Instagram',
      active: true,
      order: 2,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  useEffect(() => {
    fetchTools();
  }, []);

  const fetchTools = async () => {
    try {
      setLoading(true);
      // In real implementation, fetch from Firebase
      // const fetchedTools = await getAITools();
      setTools(mockTools);
    } catch (error) {
      console.error('Error fetching AI tools:', error);
      toast.error('Failed to load AI tools');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const toolData = {
        ...formData,
        features: formData.features ? formData.features.split('\n').map(feature => feature.trim()).filter(Boolean) : []
      };

      if (editingTool) {
        // Update existing tool
        toast.success('AI Tool updated successfully!');
      } else {
        // Create new tool
        toast.success('AI Tool created successfully!');
      }

      await fetchTools();
      handleCloseDialog();
      onRefresh?.();
    } catch (error) {
      console.error('Error saving AI tool:', error);
      toast.error('Failed to save AI tool. Please try again.');
    }
  };

  const handleEdit = (tool: AITool) => {
    setEditingTool(tool);
    setFormData({
      title: tool.title,
      description: tool.description,
      videoUrl: tool.videoUrl,
      icon: tool.icon,
      features: tool.features.join('\n'),
      buttonText: tool.buttonText,
      active: tool.active,
      order: tool.order
    });
    setDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this AI tool?')) return;

    try {
      // In real implementation, delete from Firebase
      toast.success('AI Tool deleted successfully!');
      await fetchTools();
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting AI tool:', error);
      toast.error('Failed to delete AI tool');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingTool(null);
    setFormData({
      title: '',
      description: '',
      videoUrl: '',
      icon: '',
      features: '',
      buttonText: '',
      active: true,
      order: 0
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Bot className="h-6 w-6 text-blue-600" />
            <h2 className="text-2xl font-bold">AI Toolkit Management</h2>
          </div>
          <p className="text-muted-foreground">
            Manage AI tools and automation services displayed on the website
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add AI Tool
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingTool ? 'Edit AI Tool' : 'Add New AI Tool'}
              </DialogTitle>
              <DialogDescription>
                {editingTool ? 'Update the AI tool details' : 'Create a new AI tool for the toolkit section'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Tool Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="WhatsApp Business Automation"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="buttonText">Button Text</Label>
                  <Input
                    id="buttonText"
                    value={formData.buttonText}
                    onChange={(e) => handleInputChange('buttonText', e.target.value)}
                    placeholder="Automate WhatsApp"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe what this AI tool does and its benefits..."
                  rows={3}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="videoUrl">Video URL</Label>
                <Input
                  id="videoUrl"
                  value={formData.videoUrl}
                  onChange={(e) => handleInputChange('videoUrl', e.target.value)}
                  placeholder="https://example.com/video.mp4"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="icon">Icon Name</Label>
                  <Input
                    id="icon"
                    value={formData.icon}
                    onChange={(e) => handleInputChange('icon', e.target.value)}
                    placeholder="MessageCircle, Bot, Zap, etc."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="order">Display Order</Label>
                  <Input
                    id="order"
                    type="number"
                    value={formData.order}
                    onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 0)}
                    placeholder="1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="features">Features (one per line)</Label>
                <Textarea
                  id="features"
                  value={formData.features}
                  onChange={(e) => handleInputChange('features', e.target.value)}
                  placeholder="Auto Replies&#10;Customer Support&#10;Bulk Messaging"
                  rows={4}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked) => handleInputChange('active', checked)}
                />
                <Label htmlFor="active">Active (visible on website)</Label>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCloseDialog}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  {editingTool ? 'Update Tool' : 'Create Tool'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tools</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tools.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tools</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tools.filter(tool => tool.active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Featured Tools</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tools.filter(tool => tool.order <= 3).length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tools Table */}
      <Card>
        <CardHeader>
          <CardTitle>AI Tools</CardTitle>
          <CardDescription>
            Manage AI tools and automation services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tool</TableHead>
                <TableHead>Features</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Order</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tools.map((tool) => (
                <TableRow key={tool.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{tool.title}</div>
                      <div className="text-xs text-muted-foreground line-clamp-2">{tool.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {tool.features.slice(0, 2).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {tool.features.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{tool.features.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={tool.active ? "default" : "secondary"}>
                      {tool.active ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{tool.order}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {tool.videoUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(tool.videoUrl, '_blank')}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(tool)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(tool.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {tools.length === 0 && !loading && (
            <div className="text-center py-8">
              <Bot className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No AI Tools</h3>
              <p className="text-gray-600 mb-4">
                Create your first AI tool to get started.
              </p>
              <Button onClick={() => setDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add AI Tool
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
