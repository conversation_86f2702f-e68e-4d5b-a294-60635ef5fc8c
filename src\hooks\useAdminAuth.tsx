'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface AdminAuthState {
  isAdmin: boolean;
  isLoading: boolean;
  isAuthorized: boolean;
}

export const useAdminAuth = () => {
  const { user, userData, loading } = useAuth();
  const router = useRouter();
  const [authState, setAuthState] = useState<AdminAuthState>({
    isAdmin: false,
    isLoading: true,
    isAuthorized: false,
  });

  useEffect(() => {
    const checkAdminAuth = () => {
      // Still loading auth data
      if (loading) {
        setAuthState({
          isAdmin: false,
          isLoading: true,
          isAuthorized: false,
        });
        return;
      }

      // No user logged in
      if (!user) {
        setAuthState({
          isAdmin: false,
          isLoading: false,
          isAuthorized: false,
        });
        // Redirect to login with return URL
        router.push('/auth?redirect=/team/admin');
        return;
      }

      // User logged in but no user data yet
      if (!userData) {
        setAuthState({
          isAdmin: false,
          isLoading: true,
          isAuthorized: false,
        });
        return;
      }

      // Check if user is admin
      const isAdmin = userData.role === 'admin';
      setAuthState({
        isAdmin,
        isLoading: false,
        isAuthorized: isAdmin,
      });

      // Redirect non-admin users
      if (!isAdmin) {
        router.push('/');
      }
    };

    checkAdminAuth();
  }, [user, userData, loading, router]);

  return authState;
};

// Higher-order component for admin route protection
export const withAdminAuth = <P extends object>(
  WrappedComponent: React.ComponentType<P>
) => {
  const AdminProtectedComponent = (props: P) => {
    const { isAuthorized, isLoading } = useAdminAuth();

    if (isLoading) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Verifying admin access...</p>
          </div>
        </div>
      );
    }

    if (!isAuthorized) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </div>
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };

  AdminProtectedComponent.displayName = `withAdminAuth(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return AdminProtectedComponent;
};
