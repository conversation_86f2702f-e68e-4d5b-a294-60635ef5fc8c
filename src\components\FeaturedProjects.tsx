'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Calendar, ArrowRight } from 'lucide-react';
import Image from 'next/image';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Project } from '@/types/project';


export const FeaturedProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFeaturedProjects();
  }, []);

  const fetchFeaturedProjects = async () => {
    try {
      setLoading(true);
      console.log('Fetching featured projects from Firebase...');

      // Try with orderBy first
      let projectsData: Project[] = [];
      try {
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active'),
          where('featured', '==', true),
          orderBy('order', 'asc')
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];
      } catch (indexError) {
        console.log('OrderBy failed, trying without orderBy');
        // Fallback: fetch without orderBy
        const projectsQuery = query(
          collection(db, 'projects'),
          where('status', '==', 'active'),
          where('featured', '==', true)
        );
        const querySnapshot = await getDocs(projectsQuery);
        projectsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        })) as Project[];

        // Sort manually
        projectsData.sort((a, b) => (a.order || 0) - (b.order || 0));
      }

      // If no featured projects found, get first 3 active projects
      if (projectsData.length === 0) {
        console.log('No featured projects found, fetching first 3 active projects');
        try {
          const fallbackQuery = query(
            collection(db, 'projects'),
            where('status', '==', 'active'),
            orderBy('order', 'asc')
          );
          const querySnapshot = await getDocs(fallbackQuery);
          projectsData = querySnapshot.docs.slice(0, 3).map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date(),
            updatedAt: doc.data().updatedAt?.toDate() || new Date(),
          })) as Project[];
        } catch (fallbackError) {
          console.log('Fallback query failed, using basic query');
          const basicQuery = query(collection(db, 'projects'));
          const querySnapshot = await getDocs(basicQuery);
          projectsData = querySnapshot.docs
            .filter(doc => doc.data().status === 'active')
            .slice(0, 3)
            .map(doc => ({
              id: doc.id,
              ...doc.data(),
              createdAt: doc.data().createdAt?.toDate() || new Date(),
              updatedAt: doc.data().updatedAt?.toDate() || new Date(),
            })) as Project[];
        }
      }

      setProjects(projectsData);
      console.log('Fetched featured projects:', projectsData.length);
    } catch (error) {
      console.error('Error fetching featured projects:', error);
      // Don't use fallback data - show empty if no real data
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading featured projects...</p>
          </div>
        </div>
      </section>
    );
  }

  if (projects.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Success Stories
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            A glimpse into the <span className="text-blue-600 font-semibold">websites</span> that we have built.
          </p>
        </div>

        {/* Projects Grid */}
        {/* Mobile Grid */}
        <div className="block md:hidden mb-12">
          <div className="grid grid-cols-1 gap-6">
            {projects.map((project) => (
              <Card key={project.id} className="relative h-full flex flex-col antialiased rounded-xl border border-white/5 p-px overflow-hidden cursor-pointer bg-white">
                {/* Image Container - First on mobile */}
                <div className="px-2 pt-2 sm:px-6 sm:pt-2 bg-dot-slate-200 bg-transparent order-1">
                  <Image
                    src={project.images[0] || 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop'}
                    alt={project.title}
                    width={400}
                    height={300}
                    className="transition duration-300 blur-0 object-contain md:object-cover rounded-lg shadow-xl h-full w-full min-h-72 md:min-h-96 bg-gray-50"
                    unoptimized
                  />

                  {/* Category Badge */}
                  <div className="absolute top-5 left-5">
                    <Badge className="bg-blue-600 text-white border-0 text-xs font-medium">
                      {project.category}
                    </Badge>
                  </div>
                </div>

                {/* Content - Second on mobile */}
                <CardContent className="p-6 order-2">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center text-xs text-gray-500">
                      <Calendar className="w-3 h-3 mr-1" />
                      {new Date(project.createdAt).toLocaleDateString()}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {project.title}
                  </h3>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {project.description}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.slice(0, 3).map((tech, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tech}
                      </Badge>
                    ))}
                    {project.technologies.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{project.technologies.length - 3} more
                      </Badge>
                    )}
                  </div>

                  {project.livePreviewUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700"
                      onClick={() => window.open(project.livePreviewUrl, '_blank')}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Live Project
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Desktop Grid */}
        <div className="hidden md:grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {projects.map((project) => (
            <Card key={project.id} className="relative h-full flex flex-col overflow-hidden cursor-pointer bg-white border-0 shadow-lg rounded-xl">
              {/* Image Container - First on desktop */}
              <div className="relative overflow-hidden bg-gray-50">
                <Image
                  src={project.images[0] || 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop'}
                  alt={project.title}
                  width={600}
                  height={400}
                  className="w-full h-64 md:h-80 object-cover"
                  unoptimized
                />

                {/* Category Badge */}
                <div className="absolute top-4 left-4">
                  <Badge className="bg-blue-600/90 backdrop-blur-sm text-white border-0 text-xs font-medium shadow-lg">
                    {project.category}
                  </Badge>
                </div>
              </div>

              {/* Content - Second on desktop */}
              <CardContent className="p-6 flex-1 flex flex-col">
                <div className="flex items-center text-xs text-gray-500 mb-3">
                  <Calendar className="w-3 h-3 mr-1" />
                  {new Date(project.createdAt).toLocaleDateString()}
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                  {project.title}
                </h3>

                <p className="text-gray-600 text-sm mb-4 line-clamp-3 leading-relaxed flex-1">
                  {project.description}
                </p>

                <div className="flex flex-wrap gap-2 mb-6">
                  {project.technologies.slice(0, 3).map((tech, index) => (
                    <Badge key={index} variant="outline" className="text-xs border-gray-200 text-gray-600">
                      {tech}
                    </Badge>
                  ))}
                  {project.technologies.length > 3 && (
                    <Badge variant="outline" className="text-xs border-gray-200 text-gray-600">
                      +{project.technologies.length - 3} more
                    </Badge>
                  )}
                </div>

                {project.livePreviewUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full cursor-pointer border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 font-medium"
                    onClick={() => window.open(project.livePreviewUrl, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Live Project
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link href="/work">
            <Button 
              size="lg" 
              className="cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium shadow-lg hover:shadow-xl transition-all duration-300"
            >
              View All Projects
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};
