'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import Link from 'next/link';
import {
  ArrowLeft,
  ShoppingCart,
  Filter,
  User,
  Mail,
  Phone,
  Calendar,
  MessageSquare,
  Code,
  Globe,
  CreditCard,
  Banknote,
  ExternalLink,
  Download,
  IndianRupee
} from 'lucide-react';
import {
  getContactMessages,
  updateContactMessageStatus,
  subscribeToContactMessages,
  createContactMessage
} from '@/lib/firebaseServices';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { ContactMessage } from '@/types';
import StatusDropdown from '@/components/admin/StatusDropdown';

export default function PurchaseRequestsPage() {
  const { user, userData } = useAuth();
  const [purchaseRequests, setPurchaseRequests] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState<ContactMessage | null>(null);
  const [editingRequest, setEditingRequest] = useState<ContactMessage | null>(null);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [paymentUrl, setPaymentUrl] = useState('');
  const [hostedLink, setHostedLink] = useState('');
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const messages = await getContactMessages();
        setPurchaseRequests(messages.filter(msg => msg.type === 'purchase-request'));
      } catch (error: unknown) {
        console.error('Error fetching purchase requests:', error);
        setError('Failed to load purchase requests');
      } finally {
        setLoading(false);
      }
    };

    if (user && userData?.role === 'admin') {
      fetchData();

      // Set up real-time listener
      const unsubscribe = subscribeToContactMessages((messages) => {
        setPurchaseRequests(messages.filter(msg => msg.type === 'purchase-request'));
      });

      return () => unsubscribe();
    }
  }, [user, userData]);

  const handleUpdateStatus = async (requestId: string, status: ContactMessage['status']): Promise<void> => {
    try {
      await updateContactMessageStatus(requestId, status);
      console.log('Purchase request status updated:', requestId, status);
      // The real-time listener will update the UI automatically
    } catch (error: unknown) {
      console.error('Error updating request status:', error);
      setError('Failed to update request status');
      throw error; // Re-throw to let the StatusDropdown handle the error state
    }
  };

  const handleToggleDelivery = async (requestId: string, codeDeliveryEnabled: boolean) => {
    try {
      setUpdating(true);
      const requestRef = doc(db, 'contactMessages', requestId);

      // Only include fields that have values
      const updateData: any = {
        codeDeliveryEnabled,
        updatedAt: new Date()
      };

      await updateDoc(requestRef, updateData);

      // Update local state
      setPurchaseRequests(prev => prev.map(req =>
        req.id === requestId ? { ...req, codeDeliveryEnabled } : req
      ));
    } catch (error: unknown) {
      console.error('Error updating delivery method:', error);
      setError('Failed to update delivery method');
    } finally {
      setUpdating(false);
    }
  };

  const handleTogglePaymentMethod = async (requestId: string, paymentMethod: 'cash' | 'online') => {
    try {
      setUpdating(true);
      const requestRef = doc(db, 'contactMessages', requestId);

      if (paymentMethod === 'online') {
        // For online payment, we need payment amount and payment URL
        const request = purchaseRequests.find(r => r.id === requestId);
        if (request && paymentAmount && paymentUrl) {
          const updateData = {
            paymentMethod: 'online',
            paymentAmount: parseFloat(paymentAmount),
            paymentLink: paymentUrl.trim(),
            updatedAt: new Date()
          };

          await updateDoc(requestRef, updateData);

          // Update local state
          setPurchaseRequests(prev => prev.map(req =>
            req.id === requestId ? {
              ...req,
              paymentMethod: 'online',
              paymentAmount: parseFloat(paymentAmount),
              paymentLink: paymentUrl.trim()
            } : req
          ));
        } else {
          setError('Please enter both payment amount and payment URL');
          return;
        }
      } else {
        // For cash payment
        const updateData = {
          paymentMethod: 'cash',
          updatedAt: new Date()
        };

        await updateDoc(requestRef, updateData);

        // Update local state
        setPurchaseRequests(prev => prev.map(req =>
          req.id === requestId ? { ...req, paymentMethod: 'cash' } : req
        ));
      }

      setEditingRequest(null);
      setPaymentAmount('');
      setPaymentUrl('');
    } catch (error: unknown) {
      console.error('Error updating payment method:', error);
      setError('Failed to update payment method');
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdateHostedLink = async (requestId: string) => {
    try {
      setUpdating(true);
      const requestRef = doc(db, 'contactMessages', requestId);

      // Only update if hostedLink has a value
      if (hostedLink && hostedLink.trim()) {
        const updateData = {
          hostedLink: hostedLink.trim(),
          updatedAt: new Date()
        };

        await updateDoc(requestRef, updateData);

        // Update local state
        setPurchaseRequests(prev => prev.map(req =>
          req.id === requestId ? { ...req, hostedLink: hostedLink.trim() } : req
        ));
      } else {
        setError('Please enter a valid hosted link');
        return;
      }

      setEditingRequest(null);
      setHostedLink('');
    } catch (error: unknown) {
      console.error('Error updating hosted link:', error);
      setError('Failed to update hosted link');
    } finally {
      setUpdating(false);
    }
  };

  const filteredRequests = purchaseRequests.filter(request => 
    statusFilter === 'all' || request.status === statusFilter
  );

  const getStatusCount = (status: string) => {
    return purchaseRequests.filter(req => req.status === status).length;
  };

  // Function to create test purchase request data
  const createTestPurchaseRequest = async () => {
    try {
      await createContactMessage({
        userId: 'test-user-id',
        userEmail: '<EMAIL>',
        userName: 'John Doe',
        userPhone: '******-123-4567',
        subject: 'Purchase Request for E-commerce Website Template',
        message: 'Hi, I would like to purchase the E-commerce Website Template. Please provide payment instructions and delivery details.',
        type: 'purchase-request',
        templateId: 'template-123',
        templateTitle: 'E-commerce Website Template',
        status: 'pending'
      });
      console.log('Test purchase request created successfully');
    } catch (error) {
      console.error('Error creating test purchase request:', error);
    }
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/">Go to Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button asChild variant="outline" size="sm">
              <Link href="/team/admin">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Purchase Requests
          </h1>
          <p className="text-gray-600">
            Manage template purchase requests from customers
          </p>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Stats Cards */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <ShoppingCart className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total</p>
                    <p className="text-xl font-bold">{purchaseRequests.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-yellow-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-xl font-bold">{getStatusCount('pending')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Confirmed</p>
                    <p className="text-xl font-bold">{getStatusCount('confirmed')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Approved</p>
                    <p className="text-xl font-bold">{getStatusCount('approved')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <div className="h-3 w-3 bg-gray-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-xl font-bold">{getStatusCount('completed')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filter Section */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Purchase Requests</CardTitle>
                <CardDescription>
                  Manage template purchase requests from customers
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="approved">Order Approved</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="declined">Declined</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading purchase requests...</p>
                </div>
              </div>
            )}

            {/* Requests List */}
            {!loading && (
              <div className="space-y-4">
                {filteredRequests.length > 0 ? (
                  filteredRequests.map((request) => (
                    <div 
                      key={request.id} 
                      className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => setSelectedRequest(request)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900">{request.userName}</h4>
                            <Badge variant="default" className="text-xs">
                              Purchase Request
                            </Badge>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
                            <p className="text-sm text-gray-600 flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {request.userEmail}
                            </p>
                            {request.userPhone && (
                              <p className="text-sm text-gray-600 flex items-center gap-1">
                                <Phone className="h-3 w-3" />
                                {request.userPhone}
                              </p>
                            )}
                          </div>
                          <p className="text-sm font-medium text-gray-800 mt-1">{request.subject}</p>
                          <p className="text-sm text-gray-500 mt-2 line-clamp-2">{request.message}</p>
                          {request.templateTitle && (
                            <p className="text-xs text-blue-600 mt-1">Template: {request.templateTitle}</p>
                          )}

                          {/* Toggle Buttons for Confirmed/Approved/Completed Requests */}
                          {(request.status === 'confirmed' || request.status === 'approved' || request.status === 'completed') && (
                            <div className="mt-3 space-y-2" onClick={(e) => e.stopPropagation()}>
                              {/* Delivery Method Toggle */}
                              <div className="flex items-center gap-4">
                                <span className="text-xs font-medium text-gray-700">Delivery:</span>
                                <div className="flex items-center gap-2">
                                  <Button
                                    size="sm"
                                    variant={request.codeDeliveryEnabled ? "default" : "outline"}
                                    className="h-6 px-2 text-xs"
                                    onClick={() => handleToggleDelivery(request.id, true)}
                                    disabled={updating}
                                  >
                                    <Code className="h-3 w-3 mr-1" />
                                    Code
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant={request.codeDeliveryEnabled === false ? "default" : "outline"}
                                    className="h-6 px-2 text-xs"
                                    onClick={() => handleToggleDelivery(request.id, false)}
                                    disabled={updating}
                                  >
                                    <Globe className="h-3 w-3 mr-1" />
                                    Hosted
                                  </Button>
                                </div>
                              </div>

                              {/* Payment Method Toggle */}
                              <div className="flex items-center gap-4">
                                <span className="text-xs font-medium text-gray-700">Payment:</span>
                                <div className="flex items-center gap-2">
                                  <Button
                                    size="sm"
                                    variant={request.paymentMethod === 'cash' ? "default" : "outline"}
                                    className="h-6 px-2 text-xs"
                                    onClick={() => handleTogglePaymentMethod(request.id, 'cash')}
                                    disabled={updating}
                                  >
                                    <Banknote className="h-3 w-3 mr-1" />
                                    Cash
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant={request.paymentMethod === 'online' ? "default" : "outline"}
                                    className="h-6 px-2 text-xs"
                                    onClick={() => {
                                      setEditingRequest(request);
                                      setPaymentAmount(request.paymentAmount?.toString() || '');
                                    }}
                                    disabled={updating}
                                  >
                                    <CreditCard className="h-3 w-3 mr-1" />
                                    Online
                                  </Button>
                                </div>
                              </div>

                              {/* Display Payment Link if Online */}
                              {request.paymentMethod === 'online' && request.paymentLink && (
                                <div className="flex items-center gap-2">
                                  <span className="text-xs text-gray-600">Payment Link:</span>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="h-6 px-2 text-xs"
                                    onClick={() => window.open(request.paymentLink, '_blank')}
                                  >
                                    <ExternalLink className="h-3 w-3 mr-1" />
                                    Open Link
                                  </Button>
                                </div>
                              )}

                              {/* Hosted Link Management */}
                              {request.codeDeliveryEnabled === false && (
                                <div className="flex items-center gap-2">
                                  <span className="text-xs text-gray-600">Hosted Link:</span>
                                  {request.hostedLink ? (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      className="h-6 px-2 text-xs"
                                      onClick={() => window.open(request.hostedLink, '_blank')}
                                    >
                                      <ExternalLink className="h-3 w-3 mr-1" />
                                      View Site
                                    </Button>
                                  ) : (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      className="h-6 px-2 text-xs"
                                      onClick={() => {
                                        setEditingRequest(request);
                                        setHostedLink(request.hostedLink || '');
                                      }}
                                    >
                                      Add Link
                                    </Button>
                                  )}
                                </div>
                              )}

                              {/* Code Download Link */}
                              {request.codeDeliveryEnabled && request.hostedLink && (
                                <div className="flex items-center gap-2">
                                  <span className="text-xs text-gray-600">Download:</span>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="h-6 px-2 text-xs"
                                    onClick={() => window.open(request.hostedLink, '_blank')}
                                  >
                                    <Download className="h-3 w-3 mr-1" />
                                    Download Code
                                  </Button>
                                </div>
                              )}
                            </div>
                          )}

                          <p className="text-xs text-gray-400 mt-2 flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center ml-4" onClick={(e) => e.stopPropagation()}>
                          <StatusDropdown
                            currentStatus={request.status}
                            onStatusChange={async (status) => await handleUpdateStatus(request.id, status as ContactMessage['status'])}
                            type="purchase-request"
                          />
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No purchase requests</h3>
                    <p className="text-gray-600 mb-4">
                      {statusFilter === 'all'
                        ? 'Purchase requests from customers will appear here.'
                        : `No ${statusFilter} purchase requests found.`
                      }
                    </p>
                    {statusFilter === 'all' && (
                      <Button
                        onClick={createTestPurchaseRequest}
                        variant="outline"
                        className="text-sm"
                      >
                        Create Test Purchase Request
                      </Button>
                    )}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Detailed Request View Dialog */}
        {selectedRequest && (
          <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Purchase Request Details</DialogTitle>
                <DialogDescription>
                  Complete information about this purchase request
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                {/* User Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Customer Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Name</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Email</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userEmail}</p>
                    </div>
                    {selectedRequest.userPhone && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Phone</label>
                        <p className="text-sm text-gray-900">{selectedRequest.userPhone}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-600">Status</label>
                      <div className="mt-1">
                        <StatusDropdown
                          currentStatus={selectedRequest.status}
                          onStatusChange={async (status) => await handleUpdateStatus(selectedRequest.id, status as ContactMessage['status'])}
                          type="purchase-request"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Request Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Request Details</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Subject</label>
                      <p className="text-sm text-gray-900">{selectedRequest.subject}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Message</label>
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">{selectedRequest.message}</p>
                    </div>
                    {selectedRequest.templateTitle && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Template</label>
                        <p className="text-sm text-blue-600">{selectedRequest.templateTitle}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Timeline */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Timeline</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Created At</label>
                      <p className="text-sm text-gray-900">
                        {new Date(selectedRequest.createdAt.seconds ? selectedRequest.createdAt.seconds * 1000 : selectedRequest.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Updated</label>
                      <p className="text-sm text-gray-900">
                        {new Date(selectedRequest.updatedAt?.seconds ? selectedRequest.updatedAt.seconds * 1000 : selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Payment Amount Modal */}
        {editingRequest && editingRequest.paymentMethod !== 'cash' && (
          <Dialog open={!!editingRequest} onOpenChange={() => setEditingRequest(null)}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Set Payment Details</DialogTitle>
                <DialogDescription>
                  Enter the payment amount and payment URL for online payment
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="paymentAmount">Payment Amount (₹)</Label>
                  <Input
                    id="paymentAmount"
                    type="number"
                    placeholder="Enter amount in rupees"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="paymentUrl">Payment URL</Label>
                  <Input
                    id="paymentUrl"
                    type="url"
                    placeholder="https://payment-gateway.com/pay"
                    value={paymentUrl}
                    onChange={(e) => setPaymentUrl(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleTogglePaymentMethod(editingRequest.id, 'online')}
                    disabled={updating || !paymentAmount || !paymentUrl}
                  >
                    {updating ? 'Creating...' : 'Create Payment Link'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setEditingRequest(null);
                      setPaymentAmount('');
                      setPaymentUrl('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Hosted Link Modal */}
        {editingRequest && editingRequest.codeDeliveryEnabled === false && !editingRequest.paymentMethod && (
          <Dialog open={!!editingRequest} onOpenChange={() => setEditingRequest(null)}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Add Hosted Link</DialogTitle>
                <DialogDescription>
                  Enter the URL where the hosted solution can be accessed
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="hostedLink">Hosted URL</Label>
                  <Input
                    id="hostedLink"
                    type="url"
                    placeholder="https://example.com"
                    value={hostedLink}
                    onChange={(e) => setHostedLink(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleUpdateHostedLink(editingRequest.id)}
                    disabled={updating || !hostedLink}
                  >
                    {updating ? 'Saving...' : 'Save Link'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setEditingRequest(null);
                      setHostedLink('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
