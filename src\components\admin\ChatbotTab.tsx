'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Bot, Plus, Edit, Trash2, MessageCircle, Save, X } from 'lucide-react';
import { getChatbotQ<PERSON>, createChatbotQA, updateChatbotQA, deleteChatbotQA, ChatbotQA } from '@/lib/firebaseServices';
import { toast } from 'sonner';

interface ChatbotTabProps {
  onRefresh?: () => void;
}

export default function ChatbotTab({ onRefresh }: ChatbotTabProps) {
  const [qas, setQAs] = useState<ChatbotQA[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingQA, setEditingQA] = useState<ChatbotQA | null>(null);
  const [formData, setFormData] = useState({
    question: '',
    answer: '',
    category: '',
    order: 1,
    active: true
  });

  useEffect(() => {
    fetchQAs();
  }, []);

  const fetchQAs = async () => {
    try {
      setLoading(true);
      const fetchedQAs = await getChatbotQAs();
      setQAs(fetchedQAs);
    } catch (error) {
      console.error('Error fetching chatbot QAs:', error);
      toast.error('Failed to load chatbot Q&As');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    if (!formData.question.trim()) {
      toast.error('Question is required');
      return;
    }
    if (!formData.answer.trim()) {
      toast.error('Answer is required');
      return;
    }
    if (!formData.category.trim()) {
      toast.error('Category is required');
      return;
    }

    try {
      if (editingQA) {
        await updateChatbotQA(editingQA.id, formData);
        toast.success('Q&A updated successfully!');
      } else {
        await createChatbotQA(formData);
        toast.success('Q&A created successfully!');
      }

      await fetchQAs();
      handleCloseDialog();
      onRefresh?.();
    } catch (error) {
      console.error('Error saving Q&A:', error);
      toast.error('Failed to save Q&A. Please try again.');
    }
  };

  const handleEdit = (qa: ChatbotQA) => {
    setEditingQA(qa);
    setFormData({
      question: qa.question,
      answer: qa.answer,
      category: qa.category,
      order: qa.order,
      active: qa.active
    });
    setDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this Q&A?')) return;
    
    try {
      await deleteChatbotQA(id);
      toast.success('Q&A deleted successfully!');
      await fetchQAs();
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting Q&A:', error);
      toast.error('Failed to delete Q&A. Please try again.');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingQA(null);
    setFormData({
      question: '',
      answer: '',
      category: '',
      order: 1,
      active: true
    });
  };

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Chatbot Management</h2>
            <p className="text-muted-foreground">Loading chatbot Q&As...</p>
          </div>
        </div>
        <div className="grid gap-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Bot className="h-6 w-6 text-blue-600" />
            <h2 className="text-2xl font-bold">Chatbot Management</h2>
          </div>
          <p className="text-muted-foreground">
            Manage chatbot questions and answers for customer support
          </p>
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-semibold text-blue-900 mb-2">Chatbot Icon Configuration</h3>
            <p className="text-xs text-blue-700 mb-2">
              To use a custom icon for your chatbot, add the image URL below. Leave empty to use the default icon.
            </p>
            <Input
              placeholder="https://example.com/chatbot-icon.png"
              className="text-xs"
              disabled
            />
            <p className="text-xs text-blue-600 mt-1">
              💡 Contact your developer to configure a custom chatbot icon
            </p>
          </div>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Q&A
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingQA ? 'Edit Q&A' : 'Add New Q&A'}
              </DialogTitle>
              <DialogDescription>
                {editingQA ? 'Update the question and answer' : 'Create a new question and answer for the chatbot'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="General">General</SelectItem>
                      <SelectItem value="Pricing">Pricing</SelectItem>
                      <SelectItem value="Services">Services</SelectItem>
                      <SelectItem value="Technical">Technical</SelectItem>
                      <SelectItem value="Purchase">Purchase</SelectItem>
                      <SelectItem value="Support">Support</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="order">Display Order</Label>
                  <Input
                    id="order"
                    type="number"
                    value={formData.order}
                    onChange={(e) => handleInputChange('order', parseInt(e.target.value))}
                    min="1"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="question">Question</Label>
                <Input
                  id="question"
                  value={formData.question}
                  onChange={(e) => handleInputChange('question', e.target.value)}
                  placeholder="What types of templates do you offer?"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="answer">Answer</Label>
                <Textarea
                  id="answer"
                  value={formData.answer}
                  onChange={(e) => handleInputChange('answer', e.target.value)}
                  placeholder="We offer a wide variety of templates including..."
                  rows={4}
                  required
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={formData.active}
                  onCheckedChange={(checked) => handleInputChange('active', checked)}
                />
                <Label htmlFor="active">Active (visible to users)</Label>
              </div>
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleCloseDialog}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  {editingQA ? 'Update' : 'Create'} Q&A
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Q&As</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{qas.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Q&As</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{qas.filter(qa => qa.active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Badge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{new Set(qas.map(qa => qa.category)).size}</div>
          </CardContent>
        </Card>
      </div>

      {/* Q&As Table */}
      <Card>
        <CardHeader>
          <CardTitle>Chatbot Q&As</CardTitle>
          <CardDescription>
            Manage questions and answers that appear in the chatbot
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order</TableHead>
                <TableHead>Question</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {qas.map((qa) => (
                <TableRow key={qa.id}>
                  <TableCell className="font-medium">{qa.order}</TableCell>
                  <TableCell className="max-w-xs">
                    <div className="truncate">{qa.question}</div>
                    <div className="text-xs text-muted-foreground truncate mt-1">
                      {qa.answer}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{qa.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={qa.active ? "default" : "secondary"}>
                      {qa.active ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(qa)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(qa.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {qas.length === 0 && (
            <div className="text-center py-8">
              <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Q&As found</h3>
              <p className="text-muted-foreground mb-4">
                Create your first chatbot Q&A to get started
              </p>
              <Button onClick={() => setDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Q&A
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
