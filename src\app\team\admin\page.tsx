'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  Users,
  FileText,
  ShoppingCart,
  Settings,
  BarChart3,
  Database,
  MessageSquare,
  Palette,
  CreditCard,
  Filter,
  Bot,
  Star,
  Briefcase,
  Award
} from 'lucide-react';
import TemplatesTab from '@/components/admin/TemplatesTab';
import StatusDropdown from '@/components/admin/StatusDropdown';
import ChatbotTab from '@/components/admin/ChatbotTab';
import CareersTab from '@/components/admin/CareersTab';
import InternshipsTab from '@/components/admin/InternshipsTab';
import ReviewsTab from '@/components/admin/ReviewsTab';
import ProjectsTab from '@/components/admin/ProjectsTab';
import { TeamTab } from '@/components/admin/TeamTab';
import { CertificatesTab } from '@/components/admin/CertificatesTab';
import { ApplicationsTab } from '@/components/admin/ApplicationsTab';

import Link from 'next/link';
import { toast } from 'sonner';
import {
  getDashboardStats,
  getCustomRequests,
  updateCustomRequestStatus,
  subscribeToCustomRequests,
  getContactMessages,
  updateContactMessageStatus,
  subscribeToContactMessages
} from '@/lib/firebaseServices';
import { CustomRequest, ContactMessage } from '@/types';
import type { ContactMessage as ContactMessageType } from '@/types';

interface Timestamp {
  seconds: number;
  nanoseconds: number;
}







function isCustomRequest(obj: unknown): obj is CustomRequest {
  return obj !== null && typeof obj === 'object' && 'title' in obj && 'description' in obj;
}

function isContactMessage(obj: unknown): obj is ContactMessage {
  return obj !== null && typeof obj === 'object' && 'subject' in obj && 'message' in obj;
}

function isTimestamp(obj: unknown): obj is Timestamp {
  return obj !== null && typeof obj === 'object' && 'seconds' in obj && typeof (obj as { seconds: unknown }).seconds === 'number';
}

function getDateFromTimestamp(timestamp: Timestamp | Date): Date {
  if (isTimestamp(timestamp)) {
    return new Date(timestamp.seconds * 1000);
  }
  return timestamp;
}

interface DashboardStats {
  totalUsers: number;
  totalTemplates: number;
  totalRequests: number;
  pendingRequests: number;
  totalProjects: number;
  customizations: number;
}

const AdminDashboard: React.FC = () => {
  const { user, userData } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalTemplates: 0,
    totalRequests: 0,
    pendingRequests: 0,
    totalProjects: 0,
    customizations: 0
  });
  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);
  const [contactMessages, setContactMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('templates');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState<CustomRequest | ContactMessageType | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard stats
        const dashboardStats = await getDashboardStats();
        setStats(dashboardStats);

        // Fetch custom requests - Show all data
        const requests = await getCustomRequests();
        setCustomRequests(requests);

        // Fetch contact messages - Show all data
        const messages = await getContactMessages();
        setContactMessages(messages);

      } catch (error: unknown) {
        console.error('Error fetching admin data:', error);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    if (user && userData?.role === 'admin') {
      fetchData();

      // Set up real-time listeners - Show all data
      const unsubscribeRequests = subscribeToCustomRequests((requests) => {
        setCustomRequests(requests);
      });

      const unsubscribeMessages = subscribeToContactMessages((messages) => {
        setContactMessages(messages);
      });

      return () => {
        unsubscribeRequests();
        unsubscribeMessages();
      };
    }
  }, [user, userData]);

  const handleUpdateRequestStatus = async (requestId: string, status: CustomRequest['status']) => {
    try {
      await updateCustomRequestStatus(requestId, status);
      // The real-time listener will update the UI automatically
    } catch (error: unknown) {
      console.error('Error updating request status:', error);
      setError('Failed to update request status');
    }
  };

  const handleUpdateMessageStatus = async (messageId: string, status: ContactMessage['status']) => {
    try {
      await updateContactMessageStatus(messageId, status);
      // The real-time listener will update the UI automatically
    } catch (error: unknown) {
      console.error('Error updating message status:', error);
      setError('Failed to update message status');
    }
  };

  const handleToggleDeliveryType = async (messageId: string, isCodeDelivery: boolean) => {
    try {
      if (isCodeDelivery) {
        // Ask for code repository URL
        const codeRepoUrl = prompt('Enter code repository URL (GitHub, etc.):');
        if (codeRepoUrl && codeRepoUrl.trim()) {
          await updateContactMessageStatus(messageId, 'pending', undefined, {
            codeDeliveryEnabled: true,
            codeRepoLink: codeRepoUrl.trim()
          });
          toast.success('Code repository URL added');
        } else {
          toast.error('Please enter a valid repository URL');
        }
      } else {
        // Ask for hosted solution URL
        const hostedUrl = prompt('Enter hosted solution URL:');
        if (hostedUrl && hostedUrl.trim()) {
          await updateContactMessageStatus(messageId, 'pending', undefined, {
            codeDeliveryEnabled: false,
            hostedLink: hostedUrl.trim()
          });
          toast.success('Hosted solution URL added');
        } else {
          toast.error('Please enter a valid hosted URL');
        }
      }
    } catch (error: unknown) {
      console.error('Error updating delivery type:', error);
      toast.error('Failed to update delivery type');
    }
  };

  const handleSetPaymentType = async (messageId: string, paymentType: 'cash' | 'online') => {
    try {
      if (paymentType === 'cash') {
        // Ask for price to show the user
        const amount = prompt('Enter price to show the user (in INR):');
        if (amount && !isNaN(Number(amount))) {
          await updateContactMessageStatus(messageId, 'pending', undefined, {
            paymentStatus: 'pending',
            paymentMethod: 'cash',
            paymentAmount: Number(amount)
          });
          toast.success(`Cash payment set with price: ₹${amount}`);
        } else {
          toast.error('Please enter a valid amount');
        }
      } else {
        // Prompt for payment amount and payment URL
        const amount = prompt('Enter payment amount (in INR):');
        if (amount && !isNaN(Number(amount))) {
          // Ask for payment URL (required for online payment)
          const paymentUrl = prompt('Enter payment URL (where user will make payment):');
          if (paymentUrl && paymentUrl.trim()) {
            await updateContactMessageStatus(messageId, 'pending', undefined, {
              paymentStatus: 'pending',
              paymentMethod: 'online',
              paymentAmount: Number(amount),
              paymentLink: paymentUrl.trim()
            });
            toast.success('Online payment link created');
          } else {
            toast.error('Please enter a valid payment URL');
          }
        } else {
          toast.error('Please enter a valid amount');
        }
      }
    } catch (error: unknown) {
      console.error('Error setting payment type:', error);
      toast.error('Failed to set payment type');
    }
  };

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                <Settings className="mr-3 h-8 w-8 text-blue-600" />
                Admin Dashboard
              </h1>
              <p className="text-gray-600">
                Manage your marketplace and monitor performance
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <Badge variant="outline" className="px-3 py-1">
                <Users className="w-4 h-4 mr-1" />
                {stats.totalUsers} Users
              </Badge>
              <Badge variant="outline" className="px-3 py-1">
                <FileText className="w-4 h-4 mr-1" />
                {stats.totalTemplates} Templates
              </Badge>
            </div>
          </div>
        </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

        {/* Stats Cards */}
        {!loading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            {/* Templates Card */}
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
              <CardContent className="p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div className="min-w-0 flex-1">
                    <p className="text-blue-100 text-xs sm:text-sm font-medium mb-1">Templates</p>
                    <p className="text-2xl sm:text-3xl font-bold truncate">{stats.totalTemplates}</p>
                    <p className="text-blue-100 text-xs">Available templates</p>
                  </div>
                  <div className="p-2 sm:p-3 bg-white/20 rounded-lg flex-shrink-0 ml-3">
                    <FileText className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Requests Card */}
            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium mb-1">Contact Requests</p>
                    <p className="text-3xl font-bold">{stats.totalRequests}</p>
                    <p className="text-green-100 text-xs">Customer inquiries</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <MessageSquare className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Projects Card */}
            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium mb-1">Projects</p>
                    <p className="text-3xl font-bold">{stats.totalProjects}</p>
                    <p className="text-purple-100 text-xs">Total projects</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Briefcase className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customizations Card */}
            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium mb-1">Customizations</p>
                    <p className="text-3xl font-bold">{stats.customizations}</p>
                    <p className="text-orange-100 text-xs">Total customizations</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Palette className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Users Card */}
            <Card className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-cyan-100 text-sm font-medium mb-1">Users</p>
                    <p className="text-3xl font-bold">{stats.totalUsers}</p>
                    <p className="text-cyan-100 text-xs">Site visitors</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200 bg-white rounded-lg shadow-sm">
            <div className="overflow-x-auto scrollbar-hide">
              <nav className="-mb-px flex gap-1 px-4 py-2 min-w-max">
                <button
                  onClick={() => setActiveTab('templates')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'templates'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Templates Management
                </button>
                <button
                  onClick={() => setActiveTab('requests')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'requests'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Contact Requests
                </button>
                <button
                  onClick={() => setActiveTab('purchases')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'purchases'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Purchase Requests
                </button>
                <button
                  onClick={() => setActiveTab('customizations')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'customizations'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Customizations
                </button>
                <button
                  onClick={() => setActiveTab('chatbot')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'chatbot'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Chatbot Q&A
                </button>
                <button
                  onClick={() => setActiveTab('careers')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'careers'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Careers
                </button>
                <button
                  onClick={() => setActiveTab('internships')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'internships'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Internships
                </button>
                <button
                  onClick={() => setActiveTab('reviews')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'reviews'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Reviews
                </button>
                <button
                  onClick={() => setActiveTab('projects')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'projects'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Projects
                </button>
                <button
                  onClick={() => setActiveTab('team')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'team'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Team
                </button>
                <button
                  onClick={() => setActiveTab('certificates')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'certificates'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Certificates
                </button>
                <button
                  onClick={() => setActiveTab('applications')}
                  className={`relative px-4 py-3 text-sm font-medium cursor-pointer whitespace-nowrap rounded-lg transition-all duration-200 ${
                    activeTab === 'applications'
                      ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  Applications
                </button>
              </nav>
            </div>
          </div>
        </div>

        {!loading && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Content based on active tab */}
            <div className="lg:col-span-2 order-2 lg:order-1">
              {activeTab === 'templates' && (
                <TemplatesTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Templates updated');
                }} />
              )}

              {activeTab === 'requests' && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Contact Requests</CardTitle>
                        <CardDescription>
                          Manage customer inquiries and requests
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="responded">Responded</SelectItem>
                            <SelectItem value="in-progress">In Progress</SelectItem>
                            <SelectItem value="resolved">Resolved</SelectItem>
                            <SelectItem value="declined">Declined</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {contactMessages
                        .filter(msg => msg.type === 'contact')
                        .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                        .length > 0 ? (
                        contactMessages
                          .filter(msg => msg.type === 'contact')
                          .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                          .map((message) => (
                          <div
                            key={message.id}
                            className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => setSelectedRequest(message as ContactMessageType)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{message.userName}</h4>
                                  <Badge variant="secondary" className="text-xs">
                                    Contact Inquiry
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{message.userEmail}</p>
                                {isContactMessage(message) && message.userPhone && (
                                  <p className="text-sm text-gray-600">{message.userPhone}</p>
                                )}
                                <p className="text-sm font-medium text-gray-800 mt-1">{message.subject}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{message.message}</p>
                                {message.templateTitle && (
                                  <p className="text-xs text-blue-600 mt-1">Template: {message.templateTitle}</p>
                                )}
                                <p className="text-xs text-gray-400 mt-2">
                                  {getDateFromTimestamp(message.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4" onClick={(e) => e.stopPropagation()}>
                                <StatusDropdown
                                  currentStatus={message.status}
                                  onStatusChange={(status) => handleUpdateMessageStatus(message.id, status as ContactMessage['status'])}
                                  type="contact-message"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No contact requests</h3>
                          <p className="text-gray-600">
                            Contact requests from users will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'purchases' && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Purchase Requests</CardTitle>
                        <CardDescription>
                          Manage template purchase requests from customers
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="confirmed">Confirmed</SelectItem>
                            <SelectItem value="approved">Order Approved</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                            <SelectItem value="declined">Declined</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {contactMessages
                        .filter(msg => msg.type === 'purchase-request')
                        .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                        .length > 0 ? (
                        contactMessages
                          .filter(msg => msg.type === 'purchase-request')
                          .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                          .map((message) => (
                          <div
                            key={message.id}
                            className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => setSelectedRequest(message as ContactMessageType)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{message.userName}</h4>
                                  <Badge variant="default" className="text-xs">
                                    Purchase Request
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{message.userEmail}</p>
                                {message.userPhone && (
                                  <p className="text-sm text-gray-600">{message.userPhone}</p>
                                )}
                                <p className="text-sm font-medium text-gray-800 mt-1">{message.subject}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{message.message}</p>
                                {message.templateTitle && (
                                  <p className="text-xs text-blue-600 mt-1">Template: {message.templateTitle}</p>
                                )}
                                <p className="text-xs text-gray-400 mt-2">
                                  {getDateFromTimestamp(message.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex flex-col items-end ml-4 gap-2" onClick={(e) => e.stopPropagation()}>
                                {/* Show Delivery and Payment toggles only when status is approved */}
                                {message.status === 'approved' && (
                                  <>
                                    {/* Delivery Type Toggle */}
                                    <div className="flex items-center gap-2">
                                      <span className="text-xs text-gray-500">Delivery:</span>
                                      <div className="flex bg-gray-100 rounded-lg p-1">
                                        <button
                                          className={`px-3 py-1 text-xs rounded-md transition-colors ${
                                            message.codeDeliveryEnabled
                                              ? 'bg-blue-500 text-white'
                                              : 'text-gray-600 hover:bg-gray-200'
                                          }`}
                                          onClick={() => handleToggleDeliveryType(message.id, true)}
                                        >
                                          Code
                                        </button>
                                        <button
                                          className={`px-3 py-1 text-xs rounded-md transition-colors ${
                                            !message.codeDeliveryEnabled
                                              ? 'bg-green-500 text-white'
                                              : 'text-gray-600 hover:bg-gray-200'
                                          }`}
                                          onClick={() => handleToggleDeliveryType(message.id, false)}
                                        >
                                          Hosted
                                        </button>
                                      </div>
                                    </div>

                                    {/* Payment Type Toggle */}
                                    <div className="flex items-center gap-2">
                                      <span className="text-xs text-gray-500">Payment:</span>
                                      <div className="flex bg-gray-100 rounded-lg p-1">
                                        <button
                                          className={`px-3 py-1 text-xs rounded-md transition-colors ${
                                            message.paymentMethod === 'cash'
                                              ? 'bg-orange-500 text-white'
                                              : 'text-gray-600 hover:bg-gray-200'
                                          }`}
                                          onClick={() => handleSetPaymentType(message.id, 'cash')}
                                        >
                                          Cash
                                        </button>
                                        <button
                                          className={`px-3 py-1 text-xs rounded-md transition-colors ${
                                            message.paymentMethod === 'online'
                                              ? 'bg-purple-500 text-white'
                                              : 'text-gray-600 hover:bg-gray-200'
                                          }`}
                                          onClick={() => handleSetPaymentType(message.id, 'online')}
                                        >
                                          Online
                                        </button>
                                      </div>
                                    </div>
                                  </>
                                )}

                                {/* Display payment amount and links */}
                                {message.paymentAmount && (
                                  <div className="text-xs text-gray-600">
                                    Price: ₹{message.paymentAmount}
                                  </div>
                                )}
                                {message.paymentLink && (
                                  <a
                                    href={message.paymentLink}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-600 hover:underline"
                                  >
                                    Payment Link
                                  </a>
                                )}
                                {message.codeRepoLink && (
                                  <a
                                    href={message.codeRepoLink}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-blue-600 hover:underline"
                                  >
                                    Code Repository
                                  </a>
                                )}
                                {message.hostedLink && (
                                  <a
                                    href={message.hostedLink}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-xs text-green-600 hover:underline"
                                  >
                                    Hosted Solution
                                  </a>
                                )}

                                <StatusDropdown
                                  currentStatus={message.status}
                                  onStatusChange={(status) => handleUpdateMessageStatus(message.id, status as ContactMessage['status'])}
                                  type="purchase-request"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No purchase requests</h3>
                          <p className="text-gray-600">
                            Purchase requests from customers will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'customizations' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Customization Requests</CardTitle>
                    <CardDescription>
                      Manage custom template requests
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {customRequests.length > 0 ? (
                        customRequests.map((request) => (
                          <div key={request.id} className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{request.title}</h4>
                                  {request.status === 'completed' || request.status === 'cancelled' ? (
                                    <Badge variant="default">Completed</Badge>
                                  ) : (
                                    <Badge variant="outline">Pending</Badge>
                                  )}
                                </div>
                                <p className="text-sm font-medium text-gray-900">{request.title}</p>
                                <p className="text-xs text-gray-500">{request.userEmail}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{request.description}</p>
                                {request.budget && (
                                  <p className="text-xs text-gray-400 mt-2">Budget: ${request.budget}</p>
                                )}
                                <p className="text-xs text-gray-400">
                                  {getDateFromTimestamp(request.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4">
                                <StatusDropdown
                                  currentStatus={request.status}
                                  onStatusChange={async (status) => {
                                    await handleUpdateRequestStatus(request.id, status as CustomRequest['status']);
                                  }}
                                  type="custom-request"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <Palette className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No customization requests</h3>
                          <p className="text-gray-600">
                            Custom template requests will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'chatbot' && (
                <ChatbotTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Chatbot Q&As updated');
                }} />
              )}

              {activeTab === 'careers' && (
                <CareersTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Careers updated');
                }} />
              )}

              {activeTab === 'internships' && (
                <InternshipsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Internships updated');
                }} />
              )}

              {activeTab === 'reviews' && (
                <ReviewsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Reviews updated');
                }} />
              )}

              {activeTab === 'projects' && (
                <ProjectsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Projects updated');
                }} />
              )}

              {activeTab === 'team' && (
                <TeamTab />
              )}

              {activeTab === 'certificates' && (
                <CertificatesTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Certificates updated');
                }} />
              )}

              {activeTab === 'applications' && (
                <ApplicationsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Applications updated');
                }} />
              )}


            </div>

            {/* Quick Actions */}
            <div className="order-1 lg:order-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Quick Actions</CardTitle>
                  <CardDescription>
                    Common admin tasks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={() => setActiveTab('templates')}
                    variant={activeTab === 'templates' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer text-sm"
                  >
                    <FileText className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">Templates</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full justify-start cursor-pointer"
                    onClick={() => setActiveTab('requests')}
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Contact Requests
                  </Button>

                  <Button asChild
                    variant="outline"
                    className="w-full justify-start cursor-pointer"
                  >
                    <Link href="/team/admin/purchase-requests">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Purchase Requests
                    </Link>
                  </Button>

                  <Button asChild
                    variant="outline"
                    className="w-full justify-start cursor-pointer"
                  >
                    <Link href="/team/admin/custom-requests">
                      <Palette className="mr-2 h-4 w-4" />
                      Customizations
                    </Link>
                  </Button>

                  <Button asChild
                    variant="outline"
                    className="w-full justify-start cursor-pointer"
                  >
                    <Link href="/team/admin/ai-automation">
                      <Bot className="mr-2 h-4 w-4" />
                      AI Automation
                    </Link>
                  </Button>

                  <Button
                    onClick={() => setActiveTab('reviews')}
                    variant={activeTab === 'reviews' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Star className="mr-2 h-4 w-4" />
                    Customer Reviews
                  </Button>

                  <Button
                    onClick={() => setActiveTab('team')}
                    variant={activeTab === 'team' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Team Management
                  </Button>

                  <Button
                    onClick={() => setActiveTab('certificates')}
                    variant={activeTab === 'certificates' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Award className="mr-2 h-4 w-4" />
                    Certificates
                  </Button>

                  <Button
                    onClick={() => setActiveTab('applications')}
                    variant={activeTab === 'applications' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Applications
                  </Button>

                  <Button
                    onClick={() => setActiveTab('chatbot')}
                    variant={activeTab === 'chatbot' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Bot className="mr-2 h-4 w-4" />
                    Chatbot Q&A
                  </Button>



                  <Button
                    onClick={() => setActiveTab('careers')}
                    variant={activeTab === 'careers' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Briefcase className="mr-2 h-4 w-4" />
                    Careers
                  </Button>

                  <Button
                    onClick={() => setActiveTab('internships')}
                    variant={activeTab === 'internships' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Internships
                  </Button>

                  <Button
                    onClick={() => setActiveTab('projects')}
                    variant={activeTab === 'projects' ? 'default' : 'outline'}
                    className="w-full justify-start cursor-pointer"
                  >
                    <Briefcase className="mr-2 h-4 w-4" />
                    Projects
                  </Button>
                </CardContent>
              </Card>

              {/* System Status */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    System Status
                  </CardTitle>
                  <CardDescription>
                    Current system health and statistics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Firebase</span>
                      </div>
                      <Badge variant="default" className="bg-green-500 hover:bg-green-600">Connected</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Firestore</span>
                      </div>
                      <Badge variant="default" className="bg-green-500 hover:bg-green-600">Active</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Authentication</span>
                      </div>
                      <Badge variant="default" className="bg-green-500 hover:bg-green-600">Working</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center">
                        <FileText className="w-4 h-4 mr-2 text-blue-600" />
                        <span className="text-sm font-medium text-gray-700">Total Templates</span>
                      </div>
                      <span className="text-sm font-bold text-blue-600">{stats.totalTemplates}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2 text-purple-600" />
                        <span className="text-sm font-medium text-gray-700">Total Users</span>
                      </div>
                      <span className="text-sm font-bold text-purple-600">{stats.totalUsers}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Detailed Request View Dialog */}
        {selectedRequest && (
          <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Request Details</DialogTitle>
                <DialogDescription>
                  Complete information about this request
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                {/* User Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">User Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Name</label>
                      <p className="text-sm text-gray-900">
                        {isContactMessage(selectedRequest)
                          ? (selectedRequest.userName || selectedRequest.name)
                          : selectedRequest.title}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Email</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userEmail}</p>
                    </div>
                    {isContactMessage(selectedRequest) && selectedRequest.userPhone && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Phone</label>
                        <p className="text-sm text-gray-900">{selectedRequest.userPhone}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-600">Status</label>
                      <div className="mt-1">
                        <StatusDropdown
                          currentStatus={selectedRequest.status}
                          onStatusChange={async (status) => {
                            try {
                              if (isCustomRequest(selectedRequest)) {
                                await handleUpdateRequestStatus(selectedRequest.id, status as CustomRequest['status']);
                              } else if (isContactMessage(selectedRequest)) {
                                await handleUpdateMessageStatus(selectedRequest.id, status as ContactMessage['status']);
                              }
                              setSelectedRequest(null);
                            } catch (error) {
                              console.error('Error updating status:', error);
                            }
                          }}
                          type={isCustomRequest(selectedRequest) ? 'custom-request' : 'contact-message'}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Request Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Request Details</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Subject/Title</label>
                      <p className="text-sm text-gray-900">
                        {isContactMessage(selectedRequest)
                          ? selectedRequest.subject
                          : selectedRequest.title}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Description/Message</label>
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">
                        {isContactMessage(selectedRequest)
                          ? selectedRequest.message
                          : selectedRequest.description}
                      </p>
                    </div>
                    {isContactMessage(selectedRequest) && selectedRequest.templateTitle && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Template</label>
                        <p className="text-sm text-blue-600">{selectedRequest.templateTitle}</p>
                      </div>
                    )}
                    {isCustomRequest(selectedRequest) && selectedRequest.category && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Category</label>
                        <p className="text-sm text-gray-900">{selectedRequest.category}</p>
                      </div>
                    )}
                    {isCustomRequest(selectedRequest) && selectedRequest.budget && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Budget</label>
                        <p className="text-sm text-gray-900">₹{selectedRequest.budget}</p>
                      </div>
                    )}
                    {isCustomRequest(selectedRequest) && selectedRequest.deadline && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Deadline</label>
                        <p className="text-sm text-gray-900">{
                          (selectedRequest as any).deadline?.seconds
                            ? new Date((selectedRequest as any).deadline.seconds * 1000).toLocaleDateString()
                            : new Date((selectedRequest as any).deadline).toLocaleDateString()
                        }</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Timestamps */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Timeline</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Created At</label>
                      <p className="text-sm text-gray-900">
                        {
                          isContactMessage(selectedRequest)
                            ? new Date((selectedRequest.createdAt as any)?.seconds ? (selectedRequest.createdAt as any).seconds * 1000 : selectedRequest.createdAt).toLocaleString()
                            : new Date(selectedRequest.createdAt).toLocaleString()
                        }
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Updated</label>
                      <p className="text-sm text-gray-900">
                        {
                          isContactMessage(selectedRequest)
                            ? new Date((selectedRequest.updatedAt as any)?.seconds ? (selectedRequest.updatedAt as any).seconds * 1000 : selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()
                            : new Date(selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()
                        }
                      </p>
                    </div>
                  </div>
                </div>

                {/* Admin Notes */}
                {selectedRequest.adminNotes && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold border-b pb-2">Admin Notes</h3>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <p className="text-sm text-blue-800">{selectedRequest.adminNotes}</p>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
  );
}

export default AdminDashboard;
