import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Rate limiting storage (in production, use Redis or database)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_NON_SIGNED = 50; // 50 requests per minute for non-signed users
const RATE_LIMIT_SIGNED_USER = 50; // 100 requests per minute for signed users
const ADMIN_RATE_LIMIT_MAX_REQUESTS = 1000; // Higher limit for admin users

function getRateLimitKey(request: NextRequest): string {
  // Use system IP address as the key for rate limiting
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const clientIp = request.headers.get('cf-connecting-ip'); // Cloudflare
  const remoteAddr = request.headers.get('remote-addr');

  // Priority: x-forwarded-for > cf-connecting-ip > x-real-ip > remote-addr > fallback
  const ip = forwarded
    ? forwarded.split(',')[0].trim()
    : clientIp || realIp || remoteAddr || 'system-default';

  return `rate_limit:${ip}`;
}

function isAdminRoute(pathname: string): boolean {
  return pathname.startsWith('/team/admin');
}

function checkRateLimit(key: string, isAdmin: boolean, isSignedUser: boolean): { allowed: boolean; remaining: number } {
  const now = Date.now();
  const maxRequests = isAdmin
    ? ADMIN_RATE_LIMIT_MAX_REQUESTS
    : isSignedUser
      ? RATE_LIMIT_SIGNED_USER
      : RATE_LIMIT_NON_SIGNED;

  const record = rateLimitMap.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    rateLimitMap.set(key, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    });
    return { allowed: true, remaining: maxRequests - 1 };
  }

  if (record.count >= maxRequests) {
    return { allowed: false, remaining: 0 };
  }

  record.count++;
  return { allowed: true, remaining: maxRequests - record.count };
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const isAdmin = isAdminRoute(pathname);

  // Check if user is signed in by looking for auth tokens
  const authToken = request.cookies.get('auth-token') || request.headers.get('authorization');
  const isSignedUser = !!authToken;

  // Apply rate limiting to all routes except static assets
  // Allow rate limiting for both signed and non-signed users
  if (!pathname.startsWith('/_next/') &&
      !pathname.startsWith('/favicon') &&
      !pathname.startsWith('/api/_next') &&
      !pathname.includes('.')) {

    const rateLimitKey = getRateLimitKey(request);
    const { allowed, remaining } = checkRateLimit(rateLimitKey, isAdmin, isSignedUser);

    const maxRequests = isAdmin
      ? ADMIN_RATE_LIMIT_MAX_REQUESTS
      : isSignedUser
        ? RATE_LIMIT_SIGNED_USER
        : RATE_LIMIT_NON_SIGNED;

    if (!allowed) {
      return new NextResponse('Too Many Requests', {
        status: 429,
        headers: {
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil((Date.now() + RATE_LIMIT_WINDOW) / 1000).toString(),
          'Retry-After': Math.ceil(RATE_LIMIT_WINDOW / 1000).toString(),
        },
      });
    }

    // Add rate limit headers to response
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', remaining.toString());
    response.headers.set('X-RateLimit-Reset', Math.ceil((Date.now() + RATE_LIMIT_WINDOW) / 1000).toString());

    // Check if the request is for the secure admin routes
    if (isAdmin) {
      // Add security headers for admin routes
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
      response.headers.set('X-Admin-Route', 'true');
    }

    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/team/admin/:path*',
  ],
};
