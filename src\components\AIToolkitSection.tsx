'use client';

import React, { useState, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import {
  Zap,
  Bot,
  MessageSquare,
  Table,
  Layers,
  Globe,
  Briefcase,
  Palette,
  MessageCircle,
  Send,
  Mail,
  Phone,
  FileText,
  Instagram,
} from 'lucide-react';

interface AITool {
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  icon: React.ReactNode;
  features: string[];
  buttonText: string;
}

const aiTools: AITool[] = [
  {
    id: "whatsapp",
    title: "WhatsApp Business Automation",
    description: "Automate customer conversations, responses, and notifications on WhatsApp Business.",
    videoUrl: "https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20231743.mp4?updatedAt=1756484517549",
    icon: <MessageCircle className="h-6 w-6" />,
    features: ["Auto Replies", "Customer Support", "Bulk Messaging"],
    buttonText: "Automate WhatsApp",
  },
  {
    id: "telegram",
    title: "Telegram Bot Automation",
    description: "Build Telegram bots to manage groups, automate tasks, and interact with users.",
    videoUrl: "https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20232100.mp4?updatedAt=1756484501549",
    icon: <Send className="h-6 w-6" />,
    features: ["Group Management", "Custom Commands", "Automated Alerts"],
    buttonText: "Deploy Telegram Bot",
  },
  {
    id: "gmail",
    title: "Gmail Automation",
    description: "Automate your Gmail workflows with smart filters, templates, and AI responses.",
    videoUrl: "https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20232259.mp4?updatedAt=1756484496589",
    icon: <Mail className="h-6 w-6" />,
    features: ["Auto-Responders", "Email Filtering", "Template Replies"],
    buttonText: "Automate Gmail",
  },
  {
    id: "call-excel",
    title: "Call Automation ",
    description: "Trigger automated phone calls directly from Excel or Google Sheets data.",
    videoUrl: "https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20232829.mp4?updatedAt=1756484503574",
    icon: <Phone className="h-6 w-6" />,
    features: ["Bulk Calling", "Data-Driven Triggers", "Custom Scripts"],
    buttonText: "Automate Calls",
  },
  {
    id: "invoice",
    title: "Invoice Generation Automation",
    description: "Generate and send invoices automatically from your data with AI-powered formatting.",
    videoUrl: "https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20233408.mp4?updatedAt=1756484515770",
    icon: <FileText className="h-6 w-6" />,
    features: ["Smart Templates", "PDF Export", "Auto Sending"],
    buttonText: "Generate Invoices",
  },
  {
    id: "instagram",
    title: "Instagram Automation",
    description: "Manage Instagram posting, replies, and growth strategies with automation tools.",
    videoUrl: "https://ik.imagekit.io/k68mokvyj/Screen%20Recording%202025-08-27%20232605.mp4?updatedAt=1756484506109",
    icon: <Instagram className="h-6 w-6" />,
    features: ["Auto Posting", "DM Automation", "Engagement Boost"],
    buttonText: "Automate Instagram",
  },

];

export const AIToolkitSection = () => {
  const [activeTab, setActiveTab] = useState('whatsapp');
  const [loadedVideos, setLoadedVideos] = useState<Set<string>>(new Set(['whatsapp'])); // Load first video by default
  const videoRefs = useRef<{ [key: string]: HTMLVideoElement | null }>({});
  const { user, userData } = useAuth();
  const router = useRouter();

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Mark video as loaded when tab is clicked
    if (!loadedVideos.has(value)) {
      setLoadedVideos(prev => new Set(Array.from(prev).concat(value)));
    }
  };

  const handleToolRequest = (tool: AITool) => {
    if (!user) {
      toast.error('Please login to request AI automation services');
      router.push('/auth');
      return;
    }

    if (!userData?.phoneNumber) {
      toast.error('Please complete your profile with phone number before requesting services');
      router.push('/profile');
      return;
    }

    // Create a contact request for the AI tool
    const subject = `AI Automation Request: ${tool.title}`;
    const message = `Hi, I'm interested in ${tool.title}.

Description: ${tool.description}

Key Features I'm interested in:
${tool.features.map(feature => `• ${feature}`).join('\n')}

Please contact me to discuss implementation and pricing.

Best regards,
${userData.fullName || user.email}`;

    // Redirect to contact page with pre-filled data
    const contactUrl = `/contact?subject=${encodeURIComponent(subject)}&message=${encodeURIComponent(message)}&type=ai-automation&service=${encodeURIComponent(tool.title)}`;
    router.push(contactUrl);

    toast.success(`Request submitted for ${tool.title}! We'll contact you soon.`);
  };

  const activeTool = aiTools.find(tool => tool.id === activeTab) || aiTools[0];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        {/* <div className="text-center mb-12 lg:mb-16 relative">
          <div className="inline-flex items-center justify-center w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mb-6">
            <span className="text-white font-bold text-lg">●</span>
          </div>
        </div> */}

        <div className='text-center mb-12 lg:mb-16 relative'>
          <span className='text-blue-500 text-3xl'>●</span>
          <span className='ml-2 lg:text-3xl sm:text-m font-bold'>Custom AI Chatbots and Automation</span>

        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          {/* Tab Navigation - Improved Responsive Design */}
          <div className="mb-8 w-full">
            <div className="overflow-x-auto scrollbar-hide">
              <div className="flex justify-center min-w-max px-4">
                <TabsList className="inline-flex h-auto w-auto bg-gray-100 p-1 rounded-lg">
                  {aiTools.map((tool) => (
                    <TabsTrigger
                      key={tool.id}
                      value={tool.id}
                      className="flex items-center gap-2 px-3 py-2 text-xs sm:text-sm font-medium whitespace-nowrap cursor-pointer hover:bg-gray-200 data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm transition-all duration-200 min-w-0"
                    >
                      <span className="flex-shrink-0">{tool.icon}</span>
                      <span className="hidden sm:inline truncate">{tool.title}</span>
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>
            </div>
          </div>

          {/* Video and Content */}
          <div className="bg-gray-50 rounded-2xl overflow-hidden">
            {/* Video Container */}
            <div className="relative aspect-video bg-gradient-to-br from-gray-100 to-gray-200">
              {aiTools.map((tool) => (
                <TabsContent key={tool.id} value={tool.id} className="m-0">
                  <div className="relative w-full h-full">
                    {loadedVideos.has(tool.id) ? (
                      <video
                        ref={(el) => {
                          videoRefs.current[tool.id] = el;
                        }}
                        className="w-full h-full object-cover"
                        autoPlay
                        loop
                        muted
                        playsInline
                        preload="metadata"
                      >
                        <source src={tool.videoUrl} type="video/mp4" />
                        {/* Fallback */}
                        <div className="absolute inset-0 bg-gradient-to-br from-sky-500 to-blue-500 flex items-center justify-center">
                          <div className="text-center text-white">
                            <div className="w-20 h-20 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                              {tool.icon}
                            </div>
                            <p className="text-lg font-medium">Loading {tool.title}...</p>
                          </div>
                        </div>
                      </video>
                    ) : (
                      <div className="absolute inset-0 bg-gradient-to-br from-sky-500 to-blue-500 flex items-center justify-center">
                        <div className="text-center text-white">
                          <div className="w-20 h-20 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                            {tool.icon}
                          </div>
                          <p className="text-lg font-medium">Click to load {tool.title}</p>
                        </div>
                      </div>
                    )}

                    {/* Pause/Play overlay */}
                    <div className="absolute bottom-4 right-4">
                      <div className="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                        <div className="w-2 h-2 bg-white rounded-full ml-1"></div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              ))}
            </div>

            {/* Content Below Video */}
            <div className="p-8 lg:p-12">
              <div className="flex items-start gap-4 mb-6">
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-sky-500 to-blue-500 rounded-lg flex items-center justify-center text-white">
                  {activeTool.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-3">
                    {activeTool.title}
                  </h3>
                  <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                    {activeTool.description}
                  </p>
                </div>
              </div>

              {/* Features */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Key Features:</h4>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  {activeTool.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg--500 rounded-full"></div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Button */}
              <div className="flex justify-center">
                <Button
                  className="bg-gradient-to-r from-sky-500 to-blue-500 hover:from-sky-600 hover:to-blue-600 text-white font-semibold px-8 py-3 rounded-lg transition-all duration-300 cursor-pointer"
                  onClick={() => handleToolRequest(activeTool)}
                >
                  {activeTool.buttonText}
                </Button>
              </div>
            </div>
          </div>

          {/* GET STARTED FAST Section */}
          {/* <div className="mt-16">
            <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
              GET STARTED FAST WITH PRE-BUILT TEMPLATES
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <Zap className="h-8 w-8 text-blue-600" />
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs">+</span>
                      </div>
                      <div className="w-6 h-6 bg-red-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs">○</span>
                      </div>
                    </div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Let AI handle your IT support tickets
                  </h4>
                  <div className="flex gap-2 mt-4">
                    <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                      <Zap className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                      <span className="text-green-600 text-xs font-bold">+</span>
                    </div>
                    <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-gray-600 text-xs">○</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <MessageSquare className="h-8 w-8 text-purple-600" />
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs">+</span>
                      </div>
                      <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs">○</span>
                      </div>
                    </div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Turn sales calls into coaching moments
                  </h4>
                  <div className="flex gap-2 mt-4">
                    <div className="w-8 h-8 bg-purple-100 rounded flex items-center justify-center">
                      <MessageSquare className="h-4 w-4 text-purple-600" />
                    </div>
                    <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                      <span className="text-green-600 text-xs font-bold">+</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer md:col-span-2 lg:col-span-1">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <Bot className="h-8 w-8 text-orange-600" />
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs">○</span>
                      </div>
                      <div className="w-6 h-6 bg-green-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs">+</span>
                      </div>
                    </div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Automate customer onboarding
                  </h4>
                  <div className="flex gap-2 mt-4">
                    <div className="w-8 h-8 bg-orange-100 rounded flex items-center justify-center">
                      <Bot className="h-4 w-4 text-orange-600" />
                    </div>
                    <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                      <span className="text-blue-600 text-xs">○</span>
                    </div>
                    <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center">
                      <span className="text-green-600 text-xs font-bold">+</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div> */}
        </Tabs>
      </div>
    </section>
  );
};
