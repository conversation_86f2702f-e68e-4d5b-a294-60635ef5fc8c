export interface Project {
  id: string;
  title: string;
  description: string;
  category: string;
  images: string[];
  livePreviewUrl?: string;
  technologies: string[];
  featured: boolean;
  order: number;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface ProjectFormData {
  title: string;
  description: string;
  category: string;
  images: string[];
  livePreviewUrl?: string;
  technologies: string[];
  featured: boolean;
  order: number;
  status: 'active' | 'inactive';
}
