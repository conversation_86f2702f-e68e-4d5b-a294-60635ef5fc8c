'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Lock, AlertTriangle, Loader2 } from 'lucide-react';
import Link from 'next/link';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { user, userData, loading } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [authLoading, setAuthLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      if (loading) return;

      // If no user, redirect to login
      if (!user) {
        router.push('/auth?redirect=/team/admin');
        return;
      }

      // If user data is not loaded yet, wait
      if (!userData) {
        return;
      }

      // Check if user has admin role
      if (userData.role !== 'admin') {
        setIsAuthorized(false);
        setAuthLoading(false);
        return;
      }

      setIsAuthorized(true);
      setAuthLoading(false);
    };

    checkAuth();
  }, [user, userData, loading, router]);

  // Show loading spinner while checking authentication
  if (loading || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Verifying Access</h2>
            <p className="text-gray-600 text-center">
              Please wait while we verify your admin privileges...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show access denied if not authorized
  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
              <Shield className="h-8 w-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p className="text-gray-600 text-center mb-6">
              You don't have permission to access the admin panel. This area is restricted to administrators only.
            </p>
            <div className="flex items-center gap-2 mb-6 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                If you believe this is an error, please contact support.
              </span>
            </div>
            <div className="flex gap-3">
              <Button asChild variant="outline">
                <Link href="/">
                  Go to Home
                </Link>
              </Button>
              <Button asChild>
                <Link href="/contact">
                  Contact Support
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render admin content if authorized
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-gray-700">
                Admin Panel - Secure Access
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Welcome, {userData?.fullName || user?.email}</span>
            </div>
          </div>
        </div>
      </div>
      {children}
    </div>
  );
};

export default AdminLayout;
