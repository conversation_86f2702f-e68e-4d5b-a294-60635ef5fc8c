'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Database, Users } from 'lucide-react';

export default function AdminSetupPage() {

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Admin Setup
          </h1>
          <p className="text-gray-600">
            Set up your KaleidoneX marketplace with sample data
          </p>
        </div>

        <div className="space-y-6">
          {/* Setup Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Admin Setup Complete
              </CardTitle>
              <CardDescription>
                Your KaleidoneX marketplace is ready to use
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600">
                <p>The admin setup has been completed. You can now:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Add your own templates and categories</li>
                  <li>Manage user accounts and permissions</li>
                  <li>Configure payment settings</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Instructions Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Next Steps
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm text-gray-600">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">1. Make yourself an admin</h4>
                  <p>Go to Firebase Console → Database → users collection → find your user → change role to "admin"</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">2. Add your templates</h4>
                  <p>Start adding your own templates and categories through the admin panel</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">3. Test the application</h4>
                  <p>Browse templates, test the search and filtering functionality</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">4. Set up payments</h4>
                  <p>Configure your payment processor for template purchases</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Firebase Setup Card */}
          <Card>
            <CardHeader>
              <CardTitle>Firebase Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-center justify-between">
                  <span>Authentication</span>
                  <span className="text-green-600 font-medium">✓ Configured</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Database</span>
                  <span className="text-green-600 font-medium">✓ Connected</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Security Rules</span>
                  <span className="text-yellow-600 font-medium">⚠ Check manually</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
