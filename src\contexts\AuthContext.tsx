'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  signInWithRedirect,
  getRed<PERSON>ctResult,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  sendPasswordResetEmail
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';

interface UserData {
  uid: string;
  email: string;
  role: 'admin' | 'user';
  displayName?: string;
  fullName?: string;
  phoneNumber?: string;
  countryCode?: string;
  favoriteTemplates?: string[];
  createdAt: Date;
  updatedAt?: Date;
}

interface AuthContextType {
  user: User | null;
  userData: UserData | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateUserProfile: (data: Partial<UserData>) => Promise<void>;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Handle Google redirect result first
    const handleRedirectResult = async () => {
      try {
        const result = await getRedirectResult(auth);
        if (result?.user) {
          // User signed in via redirect, create/update user document
          const userDoc = await getDoc(doc(db, 'users', result.user.uid));
          if (!userDoc.exists()) {
            const userData: UserData = {
              uid: result.user.uid,
              email: result.user.email!,
              role: 'user',
              displayName: result.user.displayName || '',
              createdAt: new Date()
            };
            await setDoc(doc(db, 'users', result.user.uid), userData);
          }
        }
      } catch (error) {
        console.error('Error handling redirect result:', error);
      }
    };

    handleRedirectResult();

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setUser(user);
        try {
          // Fetch user data from Firestore
          const userDoc = await getDoc(doc(db, 'users', user.uid));
          if (userDoc.exists()) {
            setUserData(userDoc.data() as UserData);
          } else {
            // Create user document if it doesn't exist (for existing users)
            const userData: UserData = {
              uid: user.uid,
              email: user.email!,
              role: 'user',
              displayName: user.displayName || '',
              createdAt: new Date()
            };
            await setDoc(doc(db, 'users', user.uid), userData);
            setUserData(userData);
          }
        } catch (error: unknown) {
          console.error('Error fetching/creating user data:', error);
          // Set basic user data even if Firestore fails
          setUserData({
            uid: user.uid,
            email: user.email!,
            role: 'user',
            displayName: user.displayName || '',
            createdAt: new Date()
          });
        }
      } else {
        setUser(null);
        setUserData(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error: unknown) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      const { user } = await createUserWithEmailAndPassword(auth, email, password);

      // Create user document in Firestore
      const userData: UserData = {
        uid: user.uid,
        email: user.email!,
        role: 'user', // Default to user role
        displayName: user.displayName || '',
        createdAt: new Date()
      };

      await setDoc(doc(db, 'users', user.uid), userData);
    } catch (error: unknown) {
      console.error('Sign up error:', error);
      throw error;
    }
  };

  const signInWithGoogle = async () => {
    try {
      const provider = new GoogleAuthProvider();
      provider.setCustomParameters({
        prompt: 'select_account'
      });

      // Try popup first, fallback to redirect if popup fails
      try {
        const result = await signInWithPopup(auth, provider);
        if (result.user) {
          // Create user document if it doesn't exist
          const userDoc = await getDoc(doc(db, 'users', result.user.uid));
          if (!userDoc.exists()) {
            const userData: UserData = {
              uid: result.user.uid,
              email: result.user.email!,
              role: 'user',
              displayName: result.user.displayName || '',
              createdAt: new Date()
            };
            await setDoc(doc(db, 'users', result.user.uid), userData);
          }
        }
      } catch (popupError: any) {
        // If popup is blocked or fails, fallback to redirect
        if (popupError.code === 'auth/popup-blocked' || popupError.code === 'auth/popup-closed-by-user') {
          await signInWithRedirect(auth, provider);
        } else {
          throw popupError;
        }
      }
    } catch (error: unknown) {
      console.error('Google sign in error:', error);
      throw error;
    }
  };

  const logout = async () => {
    await signOut(auth);
  };

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: unknown) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  const updateUserProfile = async (data: Partial<UserData>) => {
    if (!user) throw new Error('No user logged in');

    try {
      const updatedData = {
        ...data,
        updatedAt: new Date()
      };

      await setDoc(doc(db, 'users', user.uid), updatedData, { merge: true });

      // Update local userData state
      if (userData) {
        setUserData({ ...userData, ...updatedData });
      }
    } catch (error: unknown) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  };

  const refreshUserData = async () => {
    if (!user) return;

    try {
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (userDoc.exists()) {
        setUserData(userDoc.data() as UserData);
      }
    } catch (error: unknown) {
      console.error('Error refreshing user data:', error);
    }
  };

  const value = {
    user,
    userData,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    logout,
    resetPassword,
    updateUserProfile,
    refreshUserData
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
