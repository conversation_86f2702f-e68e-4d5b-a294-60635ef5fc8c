'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { X, Plus, FileText, DollarSign, Tag, Code, Link, Star } from 'lucide-react';
import { Template } from '@/types';
import { collection, addDoc, doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { toast } from '@/lib/toast';

interface TemplateDialogProps {
  open: boolean;
  onClose: () => void;
  template?: Template | null;
  onSuccess: () => void;
}

export default function EnhancedTemplateDialog({ open, onClose, template, onSuccess }: TemplateDialogProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    shortDescription: '',
    detailedDescription: '',
    category: '',
    price: '',
    minPrice: '',
    maxPrice: '',
    originalPrice: '',
    imageUrl: '',
    previewUrl: '',
    demoUrl: '',
    downloadUrl: '',
    featured: false,
    premium: false,
    free: false,
    version: '1.0.0',
    setupTime: '',
    difficultyLevel: 'Beginner',
    licenseType: 'Standard License',
    keyFeatures: '',
    technologyStack: ''
  });

  useEffect(() => {
    if (template) {
      setFormData({
        title: template.title || '',
        shortDescription: template.description || '',
        detailedDescription: template.detailedDescription || '',
        category: template.category || '',
        price: template.price?.toString() || '',
        minPrice: template.minPrice?.toString() || '',
        maxPrice: template.maxPrice?.toString() || '',
        originalPrice: template.originalPrice?.toString() || '',
        imageUrl: template.imageUrl || '',
        previewUrl: template.previewUrl || '',
        demoUrl: template.demoUrl || '',
        downloadUrl: template.downloadUrl || '',
        featured: template.featured || false,
        premium: template.premium || false,
        free: template.free || false,
        version: template.version || '1.0.0',
        setupTime: template.setupTime || '',
        difficultyLevel: template.difficultyLevel || 'Beginner',
        licenseType: template.licenseType || 'Standard License',
        keyFeatures: template.keyFeatures || '',
        technologyStack: template.technologyStack || ''
      });
    } else {
      setFormData({
        title: '',
        shortDescription: '',
        detailedDescription: '',
        category: '',
        price: '',
        minPrice: '',
        maxPrice: '',
        originalPrice: '',
        imageUrl: '',
        previewUrl: '',
        demoUrl: '',
        downloadUrl: '',
        featured: false,
        premium: false,
        free: false,
        version: '1.0.0',
        setupTime: '',
        difficultyLevel: 'Beginner',
        licenseType: 'Standard License',
        keyFeatures: '',
        technologyStack: ''
      });
    }
  }, [template, open]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Auto-calculate discount
  const calculateDiscount = () => {
    if (formData.price && formData.originalPrice) {
      const price = parseFloat(formData.price);
      const originalPrice = parseFloat(formData.originalPrice);
      if (originalPrice > price) {
        return Math.round(((originalPrice - price) / originalPrice) * 100);
      }
    }
    return 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const templateData = {
        title: formData.title,
        description: formData.shortDescription,
        detailedDescription: formData.detailedDescription,
        category: formData.category,
        price: parseFloat(formData.price) || 0,
        minPrice: formData.minPrice ? parseFloat(formData.minPrice) : undefined,
        maxPrice: formData.maxPrice ? parseFloat(formData.maxPrice) : undefined,
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
        imageUrl: formData.imageUrl,
        previewUrl: formData.previewUrl || undefined,
        demoUrl: formData.demoUrl || undefined,
        downloadUrl: formData.downloadUrl || undefined,
        featured: formData.featured,
        premium: formData.premium,
        free: formData.free,
        version: formData.version,
        setupTime: formData.setupTime,
        difficultyLevel: formData.difficultyLevel,
        licenseType: formData.licenseType,
        keyFeatures: formData.keyFeatures,
        technologyStack: formData.technologyStack,
        discount: calculateDiscount(),
        rating: template?.rating || 0,
        downloads: template?.downloads || 0,
        updatedAt: new Date(),
        createdBy: 'admin'
      };

      if (template) {
        await updateDoc(doc(db, 'templates', template.id), templateData);
        toast.success('Template updated successfully');
      } else {
        await addDoc(collection(db, 'templates'), {
          ...templateData,
          createdAt: new Date()
        });
        toast.success('Template added successfully');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to save template');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="bg-gray-50 -m-6 mb-6 p-6 border-b">
          <DialogTitle className="flex items-center gap-2 text-2xl text-gray-900">
            <Plus className="h-6 w-6 text-gray-700" />
            {template ? 'Edit Template' : 'Add New Template'}
          </DialogTitle>
          <DialogDescription className="text-base text-gray-600">
            {template ? 'Update template details and pricing information' : 'Create a new template with all the necessary details and pricing information'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-8 p-2">
          {/* Basic Information Section */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2 flex items-center gap-2">
              <FileText className="h-5 w-5 text-gray-700" />
              Basic Information
            </h3>
            <p className="text-sm text-gray-600 -mt-2">Essential template details and descriptions</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Template Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter template title..."
                  required
                />
              </div>
              <div>
                <Label htmlFor="urlSlug">URL Slug</Label>
                <Input
                  id="urlSlug"
                  value={formData.title.toLowerCase().replace(/\s+/g, '-')}
                  placeholder="auto-generated-from-title"
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500 mt-1">Auto-generated from title if left empty</p>
              </div>
            </div>

            <div>
              <Label htmlFor="shortDescription">Short Description *</Label>
              <Textarea
                id="shortDescription"
                value={formData.shortDescription}
                onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                placeholder="Brief description for template cards and listings..."
                rows={2}
                maxLength={150}
                required
              />
              <p className="text-xs text-gray-500 mt-1">This appears on template cards (max 150 characters recommended)</p>
            </div>

            <div>
              <Label htmlFor="detailedDescription">Detailed Description</Label>
              <Textarea
                id="detailedDescription"
                value={formData.detailedDescription}
                onChange={(e) => handleInputChange('detailedDescription', e.target.value)}
                placeholder="Comprehensive description including features, use cases, and benefits..."
                rows={4}
              />
              <p className="text-xs text-gray-500 mt-1">Detailed description for template detail pages</p>
            </div>
          </div>

          {/* Pricing & Revenue Section */}
          <div className="space-y-4 bg-gray-50 p-4 rounded-lg border">
            <div className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <DollarSign className="h-5 w-5 text-gray-700" />
              Pricing & Revenue
            </div>
            <p className="text-sm text-gray-600">Set competitive pricing with automatic discount calculation</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <Label htmlFor="price">Current Price (₹) *</Label>
                <Input
                  id="price"
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="999"
                  min="0"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">Final price customers will pay</p>
              </div>
              <div>
                <Label htmlFor="originalPrice">Original Price (₹) <span className="text-gray-500">Optional</span></Label>
                <Input
                  id="originalPrice"
                  type="number"
                  value={formData.originalPrice}
                  onChange={(e) => handleInputChange('originalPrice', e.target.value)}
                  placeholder="1499"
                  min="0"
                />
                <p className="text-xs text-gray-500 mt-1">Higher price to show discount value</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="minPrice">Min Price Range (₹) <span className="text-gray-500">Optional</span></Label>
                <Input
                  id="minPrice"
                  type="number"
                  value={formData.minPrice}
                  onChange={(e) => handleInputChange('minPrice', e.target.value)}
                  placeholder="1999"
                  min="0"
                />
                <p className="text-xs text-gray-500 mt-1">Starting price for price range</p>
              </div>
              <div>
                <Label htmlFor="maxPrice">Max Price Range (₹) <span className="text-gray-500">Optional</span></Label>
                <Input
                  id="maxPrice"
                  type="number"
                  value={formData.maxPrice}
                  onChange={(e) => handleInputChange('maxPrice', e.target.value)}
                  placeholder="4999"
                  min="0"
                />
                <p className="text-xs text-gray-500 mt-1">Maximum price for price range</p>
              </div>
              <div>
                <Label htmlFor="discount">Discount % <span className="text-green-600">Auto-calculated</span></Label>
                <Input
                  id="discount"
                  type="number"
                  value={calculateDiscount()}
                  placeholder="33"
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500 mt-1">Automatically calculated from prices</p>
              </div>
            </div>
          </div>

          {/* Category & Classification Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <Tag className="h-5 w-5 text-gray-700" />
              Category & Classification
            </div>
            <p className="text-sm text-gray-600">Organize and classify your template for better discovery</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Template Category *</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Technology">Technology</SelectItem>
                    <SelectItem value="Business">Business</SelectItem>
                    <SelectItem value="Education">Education</SelectItem>
                    <SelectItem value="Portfolio">Portfolio</SelectItem>
                    <SelectItem value="E-commerce">E-commerce</SelectItem>
                    <SelectItem value="Dashboard">Dashboard</SelectItem>
                    <SelectItem value="Blog">Blog</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">Choose the most relevant category</p>
              </div>
              <div>
                <Label htmlFor="difficultyLevel">Difficulty Level</Label>
                <Select value={formData.difficultyLevel} onValueChange={(value) => handleInputChange('difficultyLevel', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Beginner" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Beginner">🟢 Beginner</SelectItem>
                    <SelectItem value="Intermediate">🟡 Intermediate</SelectItem>
                    <SelectItem value="Advanced">🔴 Advanced</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">Implementation complexity level</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="version">Version</Label>
                <Input
                  id="version"
                  value={formData.version}
                  onChange={(e) => handleInputChange('version', e.target.value)}
                  placeholder="1.0.0"
                />
                <p className="text-xs text-gray-500 mt-1">Semantic version number</p>
              </div>
              <div>
                <Label htmlFor="setupTime">Setup Time</Label>
                <Input
                  id="setupTime"
                  value={formData.setupTime}
                  onChange={(e) => handleInputChange('setupTime', e.target.value)}
                  placeholder="2-3 hours"
                />
                <p className="text-xs text-gray-500 mt-1">Time to implement/customize</p>
              </div>
              <div>
                <Label htmlFor="licenseType">License Type</Label>
                <Select value={formData.licenseType} onValueChange={(value) => handleInputChange('licenseType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Standard License" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Standard License">Standard License</SelectItem>
                    <SelectItem value="Extended License">Extended License</SelectItem>
                    <SelectItem value="Commercial License">Commercial License</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">Usage rights and restrictions</p>
              </div>
            </div>
          </div>

          {/* Features & Technology Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <Code className="h-5 w-5 text-gray-700" />
              Features & Technology
            </div>
            <p className="text-sm text-gray-600">Highlight key features and technical specifications</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="keyFeatures">Key Features</Label>
                <Textarea
                  id="keyFeatures"
                  value={formData.keyFeatures}
                  onChange={(e) => handleInputChange('keyFeatures', e.target.value)}
                  placeholder="Responsive Design, Dark Mode, SEO Optimized, Mobile First, Fast Loading"
                  rows={3}
                />
                <p className="text-xs text-gray-500 mt-1">Separate features with commas. These will be displayed as badges.</p>
              </div>
              <div>
                <Label htmlFor="technologyStack">Technology Stack</Label>
                <Textarea
                  id="technologyStack"
                  value={formData.technologyStack}
                  onChange={(e) => handleInputChange('technologyStack', e.target.value)}
                  placeholder="Next.js 15, TypeScript, Tailwind CSS, Framer Motion, Supabase"
                  rows={3}
                />
                <p className="text-xs text-gray-500 mt-1">List technologies used. Helps developers understand requirements.</p>
              </div>
            </div>
          </div>

          {/* URLs & Media Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <Link className="h-5 w-5 text-gray-700" />
              URLs & Media
            </div>
            <p className="text-sm text-gray-600">Add preview images and demo links</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="imageUrl">Preview Image URL *</Label>
                <Input
                  id="imageUrl"
                  type="url"
                  value={formData.imageUrl}
                  onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                  placeholder="https://example.com/template-preview.jpg"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">High-quality image for template cards (recommended: 800x600px)</p>
              </div>
              <div>
                <Label htmlFor="previewUrl">Live Preview URL</Label>
                <Input
                  id="previewUrl"
                  type="url"
                  value={formData.previewUrl}
                  onChange={(e) => handleInputChange('previewUrl', e.target.value)}
                  placeholder="https://preview.example.com"
                />
                <p className="text-xs text-gray-500 mt-1">Working demo for customers to preview</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="demoUrl">Demo URL</Label>
                <Input
                  id="demoUrl"
                  type="url"
                  value={formData.demoUrl}
                  onChange={(e) => handleInputChange('demoUrl', e.target.value)}
                  placeholder="https://demo.example.com"
                />
                <p className="text-xs text-gray-500 mt-1">Interactive demo or showcase</p>
              </div>
              <div>
                <Label htmlFor="downloadUrl">Download URL</Label>
                <Input
                  id="downloadUrl"
                  type="url"
                  value={formData.downloadUrl}
                  onChange={(e) => handleInputChange('downloadUrl', e.target.value)}
                  placeholder="https://files.example.com/template.zip"
                />
                <p className="text-xs text-gray-500 mt-1">Direct download link for purchased templates</p>
              </div>
            </div>
          </div>

          {/* Template Status & Visibility Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-lg font-semibold text-gray-900">
              <Star className="h-5 w-5 text-gray-700" />
              Template Status & Visibility
            </div>
            <p className="text-sm text-gray-600">Control how your template appears to customers</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) => handleInputChange('featured', checked)}
                />
                <div>
                  <Label htmlFor="featured" className="font-medium">Featured Template</Label>
                  <p className="text-xs text-gray-500">Appears in featured sections and gets priority placement</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="premium"
                  checked={formData.premium}
                  onCheckedChange={(checked) => handleInputChange('premium', checked)}
                />
                <div>
                  <Label htmlFor="premium" className="font-medium">Premium Template</Label>
                  <p className="text-xs text-gray-500">High-quality template with advanced features</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="free"
                  checked={formData.free}
                  onCheckedChange={(checked) => handleInputChange('free', checked)}
                />
                <div>
                  <Label htmlFor="free" className="font-medium">Free Template</Label>
                  <p className="text-xs text-gray-500">Available for free download without payment</p>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <Label className="font-medium">Template Status Summary:</Label>
              <p className="text-sm text-gray-600 mt-1">
                {formData.featured && formData.premium && formData.free ? 'Featured Free Premium Template' :
                 formData.featured && formData.premium ? 'Featured Premium Template' :
                 formData.featured && formData.free ? 'Featured Free Template' :
                 formData.premium && formData.free ? 'Free Premium Template' :
                 formData.featured ? 'Featured Template' :
                 formData.premium ? 'Premium Template' :
                 formData.free ? 'Free Template' :
                 'Standard Template'}
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading} className="bg-gray-900 hover:bg-gray-800 text-white">
              {loading ? (template ? 'Updating...' : 'Creating...') : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  {template ? 'Update Template' : 'Create Template'}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
