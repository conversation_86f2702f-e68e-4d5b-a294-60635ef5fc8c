'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  Briefcase,
  GraduationCap,
  Filter,
  Calendar,
  ExternalLink,
  Loader2,
  Clock,
  FileText
} from 'lucide-react';
import {
  getApplications,
  updateApplicationStatus,
  subscribeToApplications,
  Application
} from '@/lib/firebaseServices';
import StatusDropdown from '@/components/admin/StatusDropdown';

interface ApplicationsTabProps {
  onRefresh?: () => void;
}

const getStatusConfig = (status: string) => {
  switch (status) {
    case 'pending':
      return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
    case 'reviewed':
      return { icon: Users, label: 'Reviewed', variant: 'default' as const, color: 'text-blue-600' };
    case 'shortlisted':
      return { icon: Users, label: 'Shortlisted', variant: 'secondary' as const, color: 'text-green-600' };
    case 'rejected':
      return { icon: Users, label: 'Rejected', variant: 'destructive' as const, color: 'text-red-600' };
    case 'hired':
      return { icon: Users, label: 'Hired', variant: 'default' as const, color: 'text-green-700' };
    default:
      return { icon: Clock, label: 'Pending', variant: 'outline' as const, color: 'text-yellow-600' };
  }
};

export const ApplicationsTab: React.FC<ApplicationsTabProps> = ({ onRefresh }) => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);

  useEffect(() => {
    fetchApplications();

    // Set up real-time listener
    const unsubscribe = subscribeToApplications((apps) => {
      setApplications(apps);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const fetchedApplications = await getApplications();
      setApplications(fetchedApplications);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStatus = async (applicationId: string, status: Application['status']): Promise<void> => {
    try {
      await updateApplicationStatus(applicationId, status);
      console.log('Application status updated:', applicationId, status);
    } catch (error) {
      console.error('Error updating application status:', error);
      throw error;
    }
  };

  const filteredApplications = applications.filter(app => {
    const typeMatch = typeFilter === 'all' || app.type === typeFilter;
    const statusMatch = statusFilter === 'all' || app.status === statusFilter;
    return typeMatch && statusMatch;
  });

  const getStatusCount = (status: string) => {
    return applications.filter(app => app.status === status).length;
  };

  const getTypeCount = (type: string) => {
    return applications.filter(app => app.type === type).length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Users className="h-6 w-6 text-blue-600" />
            <h2 className="text-2xl font-bold">Applications Management</h2>
          </div>
          <p className="text-muted-foreground">
            Manage career and internship applications from candidates
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      {!loading && (
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-xl font-bold">{applications.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Briefcase className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Careers</p>
                  <p className="text-xl font-bold">{getTypeCount('career')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <GraduationCap className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Internships</p>
                  <p className="text-xl font-bold">{getTypeCount('internship')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 bg-yellow-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-xl font-bold">{getStatusCount('pending')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Reviewed</p>
                  <p className="text-xl font-bold">{getStatusCount('reviewed')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="h-3 w-3 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Hired</p>
                  <p className="text-xl font-bold">{getStatusCount('hired')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filter Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Applications</CardTitle>
              <CardDescription>
                Manage career and internship applications
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="career">Careers</SelectItem>
                    <SelectItem value="internship">Internships</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="reviewed">Reviewed</SelectItem>
                    <SelectItem value="shortlisted">Shortlisted</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="hired">Hired</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading applications...</span>
            </div>
          ) : filteredApplications.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Candidate</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Applied</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-gray-400" />
                          <div>
                            <span className="font-medium">{application.userName}</span>
                            <p className="text-xs text-gray-500">{application.userEmail}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">{application.positionTitle}</span>
                      </TableCell>
                      <TableCell>
                        <Badge variant={application.type === 'career' ? 'default' : 'secondary'}>
                          {application.type === 'career' ? 'Career' : 'Internship'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-gray-400" />
                          <span className="text-sm">
                            {application.createdAt.toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <StatusDropdown
                          currentStatus={application.status}
                          onStatusChange={async (status) => await handleUpdateStatus(application.id, status as Application['status'])}
                          type="application"
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedApplication(application)}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No applications found</h3>
              <p className="text-gray-600">
                {typeFilter === 'all' && statusFilter === 'all'
                  ? 'Applications will appear here when candidates apply.'
                  : 'No applications match the selected filters.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Application Details Dialog */}
      {selectedApplication && (
        <Dialog open={!!selectedApplication} onOpenChange={() => setSelectedApplication(null)}>
          <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden">
            <DialogHeader className="border-b pb-4">
              <DialogTitle className="text-xl font-semibold">Application Details</DialogTitle>
              <DialogDescription>
                Complete information about this application
              </DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="personal" className="flex-1 overflow-hidden">
              <div className="border-b px-6 py-4">
                <TabsList className="grid grid-cols-3 gap-2 max-w-2xl mx-auto h-auto p-0 bg-transparent">
                  <TabsTrigger
                    value="personal"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all"
                  >
                    <Users className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Personal</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="professional"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all"
                  >
                    <Briefcase className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Professional</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="education"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all"
                  >
                    <GraduationCap className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Education</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="skills"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all"
                  >
                    <FileText className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Skills</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="application"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all"
                  >
                    <FileText className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Application</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="timeline"
                    className="flex flex-col items-center gap-2 text-xs py-4 px-3 min-h-[70px] bg-background border rounded-lg hover:bg-muted/50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm transition-all"
                  >
                    <Clock className="h-6 w-6 flex-shrink-0" />
                    <span className="font-medium">Timeline</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <div className="overflow-y-auto max-h-[calc(95vh-200px)] px-6 py-4">
                {/* Personal Information Tab */}
                <TabsContent value="personal" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        Personal Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Name</label>
                          <p className="text-sm font-medium">
                            {selectedApplication.userName}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Email</label>
                          <p className="text-sm font-medium">
                            {selectedApplication.userEmail}
                          </p>
                        </div>
                        {selectedApplication.userPhone && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Phone</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.userPhone}
                            </p>
                          </div>
                        )}
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Status</label>
                          <StatusDropdown
                            currentStatus={selectedApplication.status}
                            onStatusChange={async (status) => await handleUpdateStatus(selectedApplication.id, status as Application['status'])}
                            type="application"
                          />
                        </div>
                        {selectedApplication.fullName && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Full Name</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.fullName}
                            </p>
                          </div>
                        )}
                        {selectedApplication.dateOfBirth && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Date of Birth</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.dateOfBirth}
                            </p>
                          </div>
                        )}
                        {selectedApplication.gender && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Gender</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.gender}
                            </p>
                          </div>
                        )}
                        {selectedApplication.nationality && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Nationality</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.nationality}
                            </p>
                          </div>
                        )}
                        {selectedApplication.currentAddress && (
                          <div className="space-y-1 md:col-span-2">
                            <label className="text-sm font-medium text-muted-foreground">Current Address</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.currentAddress}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Professional Information Tab */}
                <TabsContent value="professional" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Briefcase className="h-5 w-5" />
                        Position & Professional Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Position</label>
                          <p className="text-sm font-medium">
                            {selectedApplication.positionTitle}
                          </p>
                        </div>
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Type</label>
                          <Badge
                            variant={selectedApplication.type === 'career' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {selectedApplication.type === 'career' ? 'Career' : 'Internship'}
                          </Badge>
                        </div>
                        {selectedApplication.currentPosition && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Current Position</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.currentPosition}
                            </p>
                          </div>
                        )}
                        {selectedApplication.currentCompany && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Current Company</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.currentCompany}
                            </p>
                          </div>
                        )}
                        {selectedApplication.totalExperience && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Total Experience</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.totalExperience}
                            </p>
                          </div>
                        )}
                        {selectedApplication.currentSalary && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Current Salary</label>
                            <p className="text-sm font-medium">
                              ₹{selectedApplication.currentSalary}
                            </p>
                          </div>
                        )}
                        {selectedApplication.expectedSalary && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Expected Salary</label>
                            <p className="text-sm font-medium">
                              ₹{selectedApplication.expectedSalary}
                            </p>
                          </div>
                        )}
                        {selectedApplication.availableStartDate && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Available Start Date</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.availableStartDate}
                            </p>
                          </div>
                        )}
                        {selectedApplication.workPreference && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Work Preference</label>
                            <p className="text-sm font-medium capitalize">
                              {selectedApplication.workPreference}
                            </p>
                          </div>
                        )}
                        {selectedApplication.willingToRelocate !== undefined && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Willing to Relocate</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.willingToRelocate ? 'Yes' : 'No'}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Education Tab */}
                <TabsContent value="education" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <GraduationCap className="h-5 w-5" />
                        Education & Qualifications
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {selectedApplication.highestQualification && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Highest Qualification</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.highestQualification}
                            </p>
                          </div>
                        )}
                        {selectedApplication.university && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">University/Institution</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.university}
                            </p>
                          </div>
                        )}
                        {selectedApplication.graduationYear && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Graduation Year</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.graduationYear}
                            </p>
                          </div>
                        )}
                        {selectedApplication.cgpa && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">CGPA/Percentage</label>
                            <p className="text-sm font-medium">
                              {selectedApplication.cgpa}
                            </p>
                          </div>
                        )}
                        {selectedApplication.additionalCertifications && (
                          <div className="space-y-1 md:col-span-2">
                            <label className="text-sm font-medium text-muted-foreground">Additional Certifications</label>
                            <p className="text-sm font-medium whitespace-pre-wrap">
                              {selectedApplication.additionalCertifications}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Skills Tab */}
                <TabsContent value="skills" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Skills & Experience
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {selectedApplication.technicalSkills && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Technical Skills</label>
                          <p className="text-sm font-medium whitespace-pre-wrap">
                            {selectedApplication.technicalSkills}
                          </p>
                        </div>
                      )}
                      {selectedApplication.softSkills && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Soft Skills</label>
                          <p className="text-sm font-medium whitespace-pre-wrap">
                            {selectedApplication.softSkills}
                          </p>
                        </div>
                      )}
                      {selectedApplication.previousProjects && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Previous Projects</label>
                          <p className="text-sm font-medium whitespace-pre-wrap">
                            {selectedApplication.previousProjects}
                          </p>
                        </div>
                      )}
                      {selectedApplication.achievements && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Achievements & Awards</label>
                          <p className="text-sm font-medium whitespace-pre-wrap">
                            {selectedApplication.achievements}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Application Materials Tab */}
                <TabsContent value="application" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Application Materials
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {selectedApplication.whyInterested && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Why Interested in This Position</label>
                          <div className="p-3 rounded-md border-l-4 border-primary bg-muted/50">
                            <p className="text-sm whitespace-pre-wrap">
                              {selectedApplication.whyInterested}
                            </p>
                          </div>
                        </div>
                      )}
                      {selectedApplication.coverLetter && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground">Cover Letter</label>
                          <div className="p-3 rounded-md border-l-4 border-primary bg-muted/50">
                            <p className="text-sm whitespace-pre-wrap">
                              {selectedApplication.coverLetter}
                            </p>
                          </div>
                        </div>
                      )}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {selectedApplication.resume && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Resume/CV</label>
                            <Button
                              asChild
                              variant="outline"
                              size="sm"
                              className="w-fit"
                            >
                              <a
                                href={selectedApplication.resume}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center gap-2"
                              >
                                <ExternalLink className="h-3 w-3" />
                                View Resume
                              </a>
                            </Button>
                          </div>
                        )}
                        {selectedApplication.portfolio && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground">Portfolio</label>
                            <Button
                              asChild
                              variant="outline"
                              size="sm"
                              className="w-fit"
                            >
                              <a
                                href={selectedApplication.portfolio}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center gap-2"
                              >
                                <ExternalLink className="h-3 w-3" />
                                View Portfolio
                              </a>
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Timeline Tab */}
                <TabsContent value="timeline" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        Timeline
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Applied At
                          </label>
                          <p className="text-sm font-medium">
                            {selectedApplication.createdAt.toLocaleString()}
                          </p>
                        </div>
                        {selectedApplication.updatedAt && (
                          <div className="space-y-1">
                            <label className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              Last Updated
                            </label>
                            <p className="text-sm font-medium">
                              {selectedApplication.updatedAt.toLocaleString()}
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </Tabs>


          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};


